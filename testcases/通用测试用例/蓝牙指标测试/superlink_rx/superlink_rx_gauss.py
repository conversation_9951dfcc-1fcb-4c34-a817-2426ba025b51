# -*- coding: utf-8 -*-
import time
import os
import itertools

from gxapp.bttengine_run_data import EventEngine, Event, BttRunData
from module_set.device_run_func_class import DeviceRun
from module_set.handle_report_class import HandleExcelReport
from module_set.test_params import sagitta_symbol_rate_chg, sagitta_scode_rate_chg, sagitta_signal_channel_chg


class CaseRun(DeviceRun):

    def __init__(self, data: BttRunData, event_engine: EventEngine):
        super().__init__(data=data, event_engine=event_engine)
        self.event_engine = event_engine
        self.data = data
        self.rf_board_init_state = False
        self.select_cmw = True
        self.case_func = "Gauss"

    def run(self):
        # 初始化设备和校验设备连接
        ret, error = self.init_device_data_and_check_connection_correct()
        if ret is False:
            return ret, error

        # 创建报告文件
        ret, error, report_path_info = self.init_cur_testcase_report_file(cur_tc_file=__file__)
        if ret is False:
            return ret, error
        else:
            self.tc_report_path = report_path_info["tc_report_path"]
            self.handle_report = HandleExcelReport(filename=self.tc_report_path)

        # 编辑当前用例流程
        self.edit_cur_testcase_test_process()

        # 测试结束，断开所有连接
        self.disconnection_device()
        return True, error

    def create_traversal_testcases_from_case_params(self):
        # 可配置的参数列表（便于后续修改）
        SYM_RATES = [0]     # [0, 1, 2]
        S_CODE_EN = [1]     # [0, 1, 2]
        SCODE_RATES = [2, 3, 4, 5, 6, 7]
        SNRS = [1, 2, 3, 4, 5, 6]
        DATA_LEN = [6]

        testcase_list = [
            {
                "data_len": dl,
                "sym_rate": sr,
                "s_code_en": sce,
                "scode_rate": scr,
                "snr": snr,
                "signal_ch": ch
            }
            for dl in DATA_LEN
            for sr in SYM_RATES
            for sce in S_CODE_EN
            for scr in SCODE_RATES
            for snr in SNRS
            for ch in sagitta_signal_channel_chg[sagitta_symbol_rate_chg[sr]]
        ]
        return testcase_list

    def edit_cur_testcase_test_process(self):
        self.write_info_log(msg="开始测试")

        traversal_testcases = self.create_traversal_testcases_from_case_params()

        for tc in traversal_testcases[:10]:
            if traversal_testcases.index(tc) == 0:
                self.rf_board_init_state = True
            else:
                self.rf_board_init_state = False
            self.write_error_log("cur_tc: {}".format(tc))
            report_sheet_name = "{}byte_{}".format(tc["data_len"], self.case_func)

            cur_sym_rate = tc["sym_rate"]
            cur_signal_ch = tc["signal_ch"]
            cur_s_code_en = tc["s_code_en"]
            cur_scode_rate = tc["scode_rate"]
            cur_snr = tc["snr"]
            cur_data_len = tc["data_len"]
            cur_rf_board_freq_offset = 0.007
            cur_test_fre = 2402 + cur_signal_ch + cur_rf_board_freq_offset

            # ret = self.cmw_api.cmw_device_query_gprf_generator_state()
            # if ret:
            #     self.write_info_log(msg="Generator State: {}".format(ret))
            #     if ret == "ON":
            #         self.cmw_api.cmw_device_set_gprf_generator_state("OFF")
            self.cmw_api.cmw_func_gprf_generator_turn_off_status()

            #
            # self.cmw_api.cmw_device_system_reset_all()
            #
            # time.sleep(1)
            #
            self.cmw_api.cmw_device_gprf_generator_set_signal_path("RF1O", "TX1")

            self.cmw_api.cmw_device_gprf_generator_set_signal_baseband_mode("ARB")

            self.cmw_api.cmw_device_gprf_generator_set_arb_file_repetition_mode("CONTinuous")

            self.cmw_api.cmw_device_gprf_generator_set_rms_level(-60)

            self.cmw_api.cmw_device_gprf_generator_set_unmod_rf_carrier_frequency(cur_test_fre, "MHz")

            ret, arb_file_path = self.cmw_api.cmw_func_gprf_generator_create_arb_file_path_from_params(
                sym_rate=cur_sym_rate, scode_rate=cur_scode_rate, snr=cur_snr, data_len=cur_data_len, test_func=self.case_func)
            if ret is False:
                error = arb_file_path
                self.write_error_log(error)
                continue
                # return ret, error

            ret, error = self.cmw_api.cmw_func_query_gprf_generator_arb_file_is_exist(arb_file_path)
            if ret is False:
                self.write_error_log(error)
                continue

            self.cmw_api.cmw_device_gprf_generator_set_arb_file(arb_file_path)

            ret = self.cmw_api.cmw_device_gprf_generator_query_arb_file()
            if ret:
                self.write_info_log("file: {}".format(ret))

            self.cmw_api.cmw_device_set_gprf_generator_state("ON")

            ret, error = self.cmw_api.cmw_func_gprf_generator_turn_on_status_wait_load_arb_file()
            if ret is False:
                self.write_error_log(error)
                continue

            if self.rf_board_init_state is True:
                self.uart_handle.interact_with_sagitta_dut_init_device()

            cur_case_params = "bb test_cmw {} {} {} {} {}".format(
                cur_sym_rate, cur_signal_ch, cur_s_code_en, cur_scode_rate, cur_data_len
            )
            ret, result = self.uart_handle.sagitta_dut_get_gauss_result(cur_case_params)
            if ret:
                self.write_info_log("result: per: {}, polar_err: {}".format(result["per"], result["polar_err"]))
                report_data = [
                    cur_sym_rate, cur_signal_ch, cur_s_code_en, cur_scode_rate, cur_snr, cur_data_len,
                    cur_test_fre, result["first_data"], result["second_data"], result["per"], result["polar_err"]
                ]
                self.handle_report.write_sagitta_test_data_with_auto_header(
                    sheet_name=report_sheet_name, test_func=self.case_func, data=report_data)

        self.write_info_log(msg="测试结束")
