import time
import os

from gxapp.bttengine_run_data import EventEngine, Event, BttRunData
from module_set.device_run_func_class import DeviceRun
from module_set.test_params import *


class CaseRun(DeviceRun):

    def __init__(self, data: BttRunData, event_engine: EventEngine):
        super().__init__(data=data, event_engine=event_engine)
        self.event_engine = event_engine
        self.data = data
        self.select_cmw = True
        self.case_func = "Gauss"

    def run(self):
        # 初始化设备和校验设备连接
        ret, error = self.init_device_data_and_check_connection_correct()
        if ret is False:
            return ret, error
        # 编辑当前用例流程
        self.edit_cur_testcase_test_process()

        # 测试结束，断开所有连接
        self.disconnection_device()
        return True, error

    def create_testcase_params(self):
        data_len_list = [6]
        sym_rate_list = [0, 1, 2]
        scode_rate_list = [2, 3, 4, 5, 6, 7]
        snr_list = [1, 2, 3, 4, 5, 6]
        case_params_list = list()
        for data_len in data_len_list:
            for sym_rate in sym_rate_list:
                signal_ch_list = sagitta_signal_channel_chg[sym_rate]
                for scode_rate in scode_rate_list:
                    for snr in snr_list:
                        for signal_ch in signal_ch_list:
                            case_params = {
                                "data_len": data_len,
                                "sym_rate": sym_rate,
                                "scode_rate": scode_rate,
                                "snr": snr,
                                "signal_ch": signal_ch
                            }
                            case_params_list.append(case_params)
        return case_params_list

    def edit_cur_testcase_test_process(self):
        self.write_info_log(msg="开始测试")

        ret = self.cmw_api.cmw_device_query_gprf_generator_state()
        if ret:
            self.write_info_log(msg="Generator State: {}".format(ret))
            if ret == "ON":
                self.cmw_api.cmw_device_set_gprf_generator_state("OFF")
        #
        # self.cmw_api.cmw_device_system_reset_all()
        #
        # time.sleep(1)
        #
        self.cmw_api.cmw_device_gprf_generator_set_signal_path("RF1O", "TX1")

        self.cmw_api.cmw_device_gprf_generator_set_signal_baseband_mode("ARB")

        self.cmw_api.cmw_device_gprf_generator_set_arb_file_repetition_mode("CONTinuous")

        self.cmw_api.cmw_device_gprf_generator_set_rms_level(-60)

        cur_sym_rate = 0

        cur_signal_ch_list = sagitta_signal_channel_chg[cur_sym_rate]

        cur_signal_ch = 0

        cur_rf_board_freq_offset = 0.007

        cur_test_fre = 2402 + cur_signal_ch + cur_rf_board_freq_offset

        cur_scode_rate = 4

        cur_snr = 2

        cur_data_len = 6

        self.cmw_api.cmw_device_gprf_generator_set_unmod_rf_carrier_frequency(cur_test_fre, "MHz")

        ret, arb_file_path = self.cmw_api.cmw_func_gprf_generator_create_arb_file_path_from_params(
            sym_rate=cur_sym_rate, scode_rate=cur_scode_rate, snr=cur_snr, data_len=cur_data_len, test_func=self.case_func)
        if ret is False:
            error = arb_file_path
            print(error)
            return ret, error

        ret, error = self.cmw_api.cmw_func_query_gprf_generator_arb_file_is_exist(arb_file_path)
        if ret is False:
            return

        self.cmw_api.cmw_device_gprf_generator_set_arb_file(arb_file_path)

        ret = self.cmw_api.cmw_device_gprf_generator_query_arb_file()
        if ret:
            self.write_info_log("file: {}".format(ret))

        self.cmw_api.cmw_device_set_gprf_generator_state("ON")

        ret, error = self.cmw_api.cmw_func_gprf_generator_turn_on_status_wait_load_arb_file()
        if ret is False:
            return

        # # self.uart_handle.interact_with_sagitta_dut_init_device()
        #
        # self.uart_handle.interact_with_sagitta_dut_configure_test_params("bb test_cmw 0 0 1 4 6")
        #
        cur_case_params = "bb test_cmw 0 0 1 4 6"
        ret, result = self.uart_handle.sagitta_dut_get_gauss_result(cur_case_params)
        # print(result)

        self.write_info_log(msg="测试结束")

