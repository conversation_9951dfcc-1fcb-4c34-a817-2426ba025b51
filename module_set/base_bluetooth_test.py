# -*- coding: utf-8 -*-
import time
import platform
import os
import threading
from datetime import datetime
from gxapp.bttengine_run_data import EventEngine, Event, BttRunData
from module_set.device_run_func_class import DeviceRun
from module_set.handle_report_class import HandleExcelReport
from module_set.test_params import sagitta_symbol_rate_chg, sagitta_signal_channel_chg
from module_set.test_params import sagitta_scode_rate_chg, arb_file_snr_chg, arb_file_str_scode_rate, arb_file_bk, arb_file_fps, arb_file_ptype

class BaseBluetoothTest(DeviceRun):
    """蓝牙测试基类，提供通用功能"""

    def __init__(self, data: BttRunData, event_engine: EventEngine):
        super().__init__(data=data, event_engine=event_engine)
        self.event_engine = event_engine
        self.data = data

        self.register_data_history = []  # 存储寄存器数据历史
        self.test_running = False  # 控制测试运行状态
        self.register_thread = None  # 寄存器数据采集线程

        self.case_func = None   # 需在子类具体用例中传递参数
        self.rf_board_init_state = False

        # 设备配置(需在子类具体用例中配置具体参数)
        self.cmw_config = dict()

        # 报告配置
        self.report_config = {
            "sheet_name_template": "{data_len}byte_{case_func}",
            # "error_sheet_name": "Error_Log"
        }

    def initialize_test_environment(self, test_case_file=None):
        """初始化测试环境，包括设备连接和报告文件创建

        Args:
            test_case_file: 测试用例文件路径，如果不提供则使用当前文件
        """
        self.write_info_log("=== 初始化测试环境 ===")

        # 初始化设备和校验设备连接
        self.write_info_log("正在初始化设备连接...")
        ret, error = self.init_device_data_and_check_connection_correct()
        if not ret:
            self.write_error_log(f"设备初始化失败: {error}")
            return False, error

        # 创建报告文件
        self.write_info_log("正在创建测试报告...")
        # 如果没有提供测试用例文件，则使用当前文件
        tc_file = test_case_file if test_case_file else __file__
        ret, error, report_path_info = self.init_cur_testcase_report_file(cur_tc_file=tc_file)
        if not ret:
            self.write_error_log(f"创建报告文件失败: {error}")
            return False, error

        self.tc_report_path = report_path_info["tc_report_path"]
        self.handle_report = HandleExcelReport(filename=self.tc_report_path)
        self.write_info_log(f"测试报告创建成功: {self.tc_report_path}")

        return True, None

    def cleanup_test_environment(self):
        """清理测试环境"""
        self.write_info_log("=== 清理测试环境 ===")
        try:
            self.disconnection_device()
            self.write_info_log("设备连接已断开")
            return True, None
        except Exception as e:
            error_msg = f"清理测试环境失败: {str(e)}"
            self.write_error_log(error_msg)
            return False, error_msg

    def setup_cmw_generator(self, test_fre):
        """配置CMW信号发生器参数"""
        self.write_info_log("开始配置CMW信号发生器...")
        try:
            self.cmw_api.cmw_func_gprf_generator_turn_off_status()
            self.cmw_api.cmw_device_gprf_generator_set_signal_path(
                self.cmw_config["signal_path"],
                self.cmw_config["tx_path"]
            )
            self.cmw_api.cmw_device_gprf_generator_set_signal_baseband_mode(
                self.cmw_config["baseband_mode"]
            )
            self.cmw_api.cmw_device_gprf_generator_set_arb_file_repetition_mode(
                self.cmw_config["repetition_mode"]
            )
            self.cmw_api.cmw_device_gprf_generator_set_rms_level(
                self.cmw_config["rms_level"]
            )
            self.cmw_api.cmw_device_gprf_generator_set_unmod_rf_carrier_frequency(
                test_fre, "MHz"
            )
            self.write_info_log(f"CMW信号发生器配置完成，频率: {test_fre}MHz")
            return True, None
        except Exception as e:
            error_msg = f"CMW信号发生器配置失败: {str(e)}"
            self.write_error_log(error_msg)
            return False, error_msg

    def sagitta_test_create_arb_file_path_from_params(
            self, sym_rate: int, scode_rate: float, snr: int, data_len: int, test_func: str):
        """
        根据参数生成 ARB 文件路径
        * sym_rate: 0-1M  1-2M  2-4M
        * scode_rate: 2-2/8  3-3/8  4-4/8  5-5/8  6-6/8  7-7/8
        * snr: 1-0dB  2-1dB  3-2dB  4-3dB  5-4dB  6-5dB
        * data_len: 数据长度，单位为 byte
        * test_func: 测试功能，eg: Gauss
        """
        error = None
        if sym_rate not in sagitta_symbol_rate_chg.keys():
            error = "sym_rate 参数错误"
            return False, error
        if scode_rate not in sagitta_scode_rate_chg.keys():
            error = "scode_rate 参数错误"
            return False, error
        if snr not in arb_file_snr_chg.keys():
            error = "snr_code 参数错误"
            return False, error

        cur_sym_rate = sagitta_symbol_rate_chg[sym_rate]
        cur_scode_rate = sagitta_scode_rate_chg[scode_rate]
        cur_snr = arb_file_snr_chg[snr]

        if test_func == "Gauss":
            fix_path = os.path.join("D:", "sle_stream", "test")
            first_path = r"{}byte_{}".format(data_len, test_func)
            second_path = r"{}M".format(cur_sym_rate)
            third_path = "R{}_polar{}".format(
                cur_sym_rate,
                arb_file_str_scode_rate[cur_scode_rate])
            if data_len == 6:
                file_name = "qpsk_{}Rs_24Fs_{}fps_PType{}_seed0_{}bk_sym{}db_grdc.wv".format(
                    cur_sym_rate,
                    arb_file_fps[data_len][cur_sym_rate],
                    arb_file_ptype[cur_scode_rate],
                    arb_file_bk[data_len][cur_scode_rate],
                    cur_snr)
            else:
                file_name = "qpsk_{}Rs_24Fs_{}fps_PType{}_seed0_{}bk_{}byte_sym{}db_grdc.wv".format(
                    cur_sym_rate,
                    arb_file_fps[data_len][cur_sym_rate],
                    arb_file_ptype[cur_scode_rate],
                    arb_file_bk[data_len][cur_scode_rate],
                    data_len,
                    cur_snr)
            arb_file_path = os.path.join(fix_path, first_path, second_path, third_path, file_name)
            print(arb_file_path)
            if platform.system() in ["Linux", "Darwin"]:
                arb_file_path = arb_file_path.replace("/", "\\")
            return True, arb_file_path
        elif test_func == "Sensitivity":
            fix_path = os.path.join("D:", "sle_stream", "test")
            first_path = r"{}byte_{}".format(data_len, test_func)
            second_path = r"{}M".format(cur_sym_rate)
            if data_len == 6:
                file_name = "qpsk_{}Rs_24Fs_{}fps_PType{}_seed0_addsine_{}bk_sym{}db_grdc.wv".format(
                    cur_sym_rate,
                    arb_file_fps[data_len][cur_sym_rate],
                    arb_file_ptype[cur_scode_rate],
                    arb_file_bk[data_len][cur_scode_rate],
                    cur_snr)
            else:
                file_name = "qpsk_{}Rs_24Fs_{}fps_PType{}_seed0_{}bk_{}byte_sym{}db_grdc.wv".format(
                    cur_sym_rate,
                    arb_file_fps[data_len][cur_sym_rate],
                    arb_file_ptype[cur_scode_rate],
                    arb_file_bk[data_len][cur_scode_rate],
                    data_len,
                    cur_snr)
            arb_file_path = os.path.join(fix_path, first_path, second_path, file_name)
            return True, arb_file_path
        else:
            error = "test_func 参数错误"
            return False, error

    def sagitta_test_load_arb_file(self, test_params):
        """加载ARB文件并验证"""
        self.write_info_log("开始加载ARB文件...")
        try:
            ret, arb_file_path = self.sagitta_test_create_arb_file_path_from_params(
                sym_rate=test_params["sym_rate"],
                scode_rate=test_params["scode_rate"],
                snr=test_params["snr"],
                data_len=test_params["data_len"],
                test_func=self.case_func
            )

            if not ret:
                return False, f"创建ARB文件路径失败: {arb_file_path}"

            ret, error = self.cmw_api.cmw_func_query_gprf_generator_arb_file_is_exist(arb_file_path)
            if not ret:
                return False, f"ARB文件不存在: {error}"

            self.cmw_api.cmw_device_gprf_generator_set_arb_file(arb_file_path)
            loaded_file = self.cmw_api.cmw_device_gprf_generator_query_arb_file()
            self.write_info_log(f"ARB文件加载成功: {loaded_file}")

            return True, None
        except Exception as e:
            error_msg = f"加载ARB文件失败: {str(e)}"
            self.write_error_log(error_msg)
            return False, error_msg

    def validate_test_params(self, params):
        """验证测试参数的有效性"""
        try:
            for key, value in params.items():
                if value is None:
                    return False, f"参数 {key} 不能为空"
                if isinstance(value, (list, tuple)) and len(value) == 0:
                    return False, f"参数 {key} 不能为空列表"
            return True, None
        except Exception as e:
            return False, f"参数验证失败: {str(e)}"

    def create_gauss_testcases(self, test_params_config):
        """创建高斯测试用例列表

        Args:
            test_params_config (dict): 高斯测试参数配置，包含以下字段：
                - SYM_RATES: 符号率配置列表 [0, 1, 2]
                - S_CODE_EN: 编码使能配置列表 [0, 1, 2]
                - SCODE_RATES: 编码率配置列表
                - SNRS: 信噪比配置列表
                - DATA_LEN: 数据长度配置列表
                - MAX_TEST_CASES: 最大测试用例数

        Returns:
            list: 生成的测试用例列表，每个用例包含完整的测试参数
        """
        self.write_info_log("=== 生成高斯测试用例 ===")

        # 验证测试参数配置
        ret, error = self.validate_test_params(test_params_config)
        if not ret:
            self.write_error_log(f"测试参数配置无效: {error}")
            return []

        try:
            # 从配置中获取参数
            params = test_params_config
            self.write_info_log("高斯测试参数配置:")
            for key, value in params.items():
                if key != "MAX_TEST_CASES":
                    self.write_info_log(f"  {key}: {value}")

            # 生成测试用例
            testcase_list = []
            for dl in params["DATA_LEN"]:
                for sr in params["SYM_RATES"]:
                    for sce in params["S_CODE_EN"]:
                        for scr in params["SCODE_RATES"]:
                            for snr in params["SNRS"]:
                                # 获取对应的信号通道
                                channels = sagitta_signal_channel_chg[sagitta_symbol_rate_chg[sr]]
                                for ch in channels:
                                    testcase = {
                                        "data_len": dl,
                                        "sym_rate": sr,
                                        "s_code_en": sce,
                                        "scode_rate": scr,
                                        "snr": snr,
                                        "signal_ch": ch
                                    }
                                    testcase_list.append(testcase)

            # 限制测试用例数量
            max_cases = params["MAX_TEST_CASES"]
            if len(testcase_list) > max_cases:
                self.write_info_log(f"测试用例数量({len(testcase_list)})超过限制({max_cases})，将只执行前{max_cases}个用例")
                testcase_list = testcase_list[:max_cases]

            self.write_info_log(f"成功生成 {len(testcase_list)} 个高斯测试用例")
            return testcase_list

        except Exception as e:
            error_msg = f"生成高斯测试用例失败: {str(e)}"
            self.write_error_log(error_msg)
            return []

    def create_sensitivity_testcases(self, test_params_config):
        """创建灵敏度测试用例列表

        Args:
            test_params_config (dict): 灵敏度测试参数配置，包含以下字段：
                - SYM_RATES: 符号率配置列表 [0, 1, 2]
                - S_CODE_EN: 编码使能配置列表 [0, 1, 2]
                - SCODE_RATES: 编码率配置列表
                - SNRS: 信噪比配置列表
                - DATA_LEN: 数据长度配置列表
                - MAX_TEST_CASES: 最大测试用例数

        Returns:
            list: 生成的测试用例列表，每个用例包含完整的测试参数
        """
        self.write_info_log("=== 生成灵敏度测试用例 ===")

        # 验证测试参数配置
        ret, error = self.validate_test_params(test_params_config)
        if not ret:
            self.write_error_log(f"测试参数配置无效: {error}")
            return []

        try:
            # 从配置中获取参数
            params = test_params_config
            self.write_info_log("灵敏度测试参数配置:")
            for key, value in params.items():
                if key != "MAX_TEST_CASES":
                    self.write_info_log(f"  {key}: {value}")

            # 生成测试用例
            testcase_list = []
            for dl in params["DATA_LEN"]:
                for sr in params["SYM_RATES"]:
                    for sce in params["S_CODE_EN"]:
                        for scr in params["SCODE_RATES"]:
                            for snr in params["SNRS"]:
                                # 获取对应的信号通道
                                channels = sagitta_signal_channel_chg[sagitta_symbol_rate_chg[sr]]
                                for ch in channels:
                                    testcase = {
                                        "data_len": dl,
                                        "sym_rate": sr,
                                        "s_code_en": sce,
                                        "scode_rate": scr,
                                        "snr": snr,
                                        "signal_ch": ch
                                    }
                                    testcase_list.append(testcase)

            # 限制测试用例数量
            max_cases = params["MAX_TEST_CASES"]
            if len(testcase_list) > max_cases:
                self.write_info_log(f"测试用例数量({len(testcase_list)})超过限制({max_cases})，将只执行前{max_cases}个用例")
                testcase_list = testcase_list[:max_cases]

            self.write_info_log(f"成功生成 {len(testcase_list)} 个灵敏度测试用例")
            return testcase_list

        except Exception as e:
            error_msg = f"生成灵敏度测试用例失败: {str(e)}"
            self.write_error_log(error_msg)
            return []

    def run(self, test_case_file=None):
        """主运行函数

        Args:
            test_case_file: 测试用例文件路径，用于报告文件创建。
                          如果不提供，将使用当前文件路径
        """
        try:
            # 初始化测试环境
            ret, error = self.initialize_test_environment(test_case_file)
            if not ret:
                return ret, error

            # 执行测试流程
            self.edit_cur_testcase_test_process()

            # 清理测试环境
            ret, error = self.cleanup_test_environment()
            if not ret:
                return ret, error

            self.write_info_log("=== 测试完成 ===")
            return True, None

        except Exception as e:
            error_msg = f"测试执行过程中发生异常: {str(e)}"
            # raise Exception(error_msg)
            self.write_error_log(error_msg)
            # 确保在发生异常时也清理环境
            self.cleanup_test_environment()
            return False, error_msg

    def edit_cur_testcase_test_process(self):
        """主测试流程，需要子类实现"""
        raise NotImplementedError

    def run_single_test(self, tc, report_sheet_name):
        """执行单次测试，需要子类实现"""
        raise NotImplementedError

    def _stop_test(self):
        """停止测试和所有相关线程"""
        self.test_running = False
        if self.register_thread and self.register_thread.is_alive():
            self.register_thread.join(timeout=3)  # 等待最多3秒
            print("Register data collection thread stopped")

    def _register_data_collector(self, reg_addr: str, h_bit: int, l_bit: int):
        """
        寄存器数据采集线程函数，每隔2秒采集一次寄存器数据
        """
        while self.test_running:
            try:
                # 先等待2秒，但要检查test_running状态以便及时退出
                for _ in range(20):  # 2秒 = 20 * 0.1秒
                    if not self.test_running:
                        break
                    time.sleep(0.1)

                # 如果测试已停止，退出循环
                if not self.test_running:
                    break

                ret, reg_value = self.uart_handle.interact_with_sagitta_dut_get_register_data(reg_addr, h_bit, l_bit)
                if ret:
                    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
                    register_entry = {
                        'timestamp': timestamp,
                        'reg_addr': reg_addr,
                        'h_bit': h_bit,
                        'l_bit': l_bit,
                        'value': reg_value
                    }
                    self.register_data_history.append(register_entry)
                    print(f"Register data collected: {register_entry}")
                else:
                    print(f"Failed to collect register data: {reg_value}")

            except Exception as e:
                print(f"Error in register data collection: {e}")
                time.sleep(0.1)

    def get_register_data_history(self):
        """获取寄存器数据历史记录"""
        return self.register_data_history.copy()

    def calc_sagitta_dut_guass_per(self, old_data: dict, cur_data: dict):
        a1 = old_data["rx_ok"]
        b1 = old_data["crc_err"]
        c1 = old_data["len_err"]
        d1 = old_data["sync_err"]
        a2 = cur_data["rx_ok"]
        b2 = cur_data["crc_err"]
        c2 = cur_data["len_err"]
        d2 = cur_data["sync_err"]
        if a1 == a2 or a1 + b1 + c1 + d1 == a2 + b2 + c2 + d2:
            error = "rx_ok same or total cnt same, can't calc per!"
            return False, error

        try:
            per = (1 - (a2 - a1) / (a2 + b2 + c2 + d2 - a1 - b1 - c1 - d1)) * 100
            return True, per
        except Exception as e:
            return False, str(e)

    def get_polar_err_from_register_data(self, register_data: list):
        """
        从寄存器数据中获取极化误差
        * register_data:
            * list: [{'timestamp': '2023-09-11 10:00:00.000', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '3.0518e-03'}, ...]
        """
        polar_err_list = list()
        for data in register_data:
            polar_err_list.append(data["value"])

        min_value = "{:.4e}".format(min((float(x) for x in polar_err_list)))
        max_value = "{:.4e}".format(max((float(x) for x in polar_err_list)))
        # print("{}~{}".format(min_value, max_value))
        return "{}~{}".format(min_value, max_value)

    def sagitta_dut_parse_gauss_read_data(self, reg_addr: str , h_bit: int = 16, l_bit: int = 0):
        """
        数据解析函数，按照新的逻辑流程执行：
        1. 等待5秒后调用一次interact_with_sagitta_dut_read_data并记录数据
        2. 启动寄存器数据采集线程
        3. 等待15秒后再次调用interact_with_sagitta_dut_read_data
        4. 计算两次数据的PER值

        Args:
            reg_addr: 寄存器地址
            h_bit: 寄存器高位数
            l_bit: 寄存器低位数
        """
        # 清空历史数据
        self.register_data_history.clear()
        self.test_running = True

        # 检查寄存器参数
        if reg_addr is None or h_bit is None or l_bit is None:
            error = "reg_addr or h_bit or l_bit is None"
            self._stop_test()
            return False, error

        try:

            while True:
                # 步骤1：等待5秒后调用一次interact_with_sagitta_dut_read_data并记录数据
                print("Step 1: Waiting 5 seconds before first data collection...")
                for _ in range(5):  # 5秒 = 50 * 0.1秒
                    if not self.test_running:
                        return False, "Test stopped during initial wait"
                    time.sleep(1)

                # 第一次数据采集
                print("Step 1: Collecting first data...")
                ret, first_data = self.uart_handle.interact_with_sagitta_dut_read_data()
                if ret is False:
                    error = first_data
                    self._stop_test()
                    return False, error

                if first_data['rx_ok'] == 0:
                    error = "rx_ok is 0"
                    self._stop_test()
                    return False, error

                if first_data['rx_ok'] > 10000:
                    print(f"First data collected: {first_data}")
                    break

            # 步骤2：启动寄存器数据采集线程
            print("Step 2: Starting register data collection thread...")
            self.register_thread = threading.Thread(
                target=self._register_data_collector,
                args=(reg_addr, h_bit, l_bit),
                daemon=True
            )
            self.register_thread.start()
            print(f"Started register data collection thread for {reg_addr}")


            while True:
                # 步骤3：等径15秒后再次调用interact_with_sagitta_dut_read_data
                print("Step 3: Waiting 15 seconds before second data collection...")
                for _ in range(6):  # 15秒 = 150 * 0.1秒
                    if not self.test_running:
                        return False, "Test stopped during 15-second wait"
                    time.sleep(1)

                # 第二次数据采集
                print("Step 3: Collecting second data...")
                ret, second_data = self.uart_handle.interact_with_sagitta_dut_read_data()
                if ret is False:
                    error = second_data
                    self._stop_test()
                    return False, error

                if second_data['rx_ok'] > 10000 and second_data['rx_ok'] - first_data['rx_ok'] > 10000:
                    print(f"Second data collected: {second_data}")
                    break

            # 步骤4：计算两次数据的PER值
            print("Step 4: Calculating PER...")
            ret_per, per = self.calc_sagitta_dut_guass_per(first_data, second_data)
            if ret_per is False:
                self._stop_test()
                return False, "calc gauss per err!"

            print(f"PER calculation completed. PER: {per}")

            # 停止测试并结束
            self._stop_test()
            self.uart_handle.interact_with_sagitta_dut_end_test()

            # 返回结果，包含PER和寄存器数据历史
            result = {
                'per': per,
                'first_data': first_data,
                'second_data': second_data,
                'register_data_history': self.register_data_history.copy(),
                'polar_err': self.get_polar_err_from_register_data(self.register_data_history.copy())
            }
            return True, result

        except Exception as e:
            print(f"Error in sagitta_dut_parse_gauss_read_data: {e}")
            self._stop_test()
            return False, str(e)
        finally:
            # 确保清理资源
            if hasattr(self, 'test_running') and self.test_running:
                self._stop_test()

    def sagitta_dut_get_gauss_result(self, params: str):

        self.uart_handle.interact_with_sagitta_dut_end_test()
        time.sleep(0.5)

        self.uart_handle.interact_with_sagitta_dut_configure_test_params(params)
        time.sleep(0.5)

        ret, result = self.sagitta_dut_parse_gauss_read_data(reg_addr="0xb40211a8", h_bit=16, l_bit=0)
        if ret is False:
            return False, result
        else:
            return True, result

    def sagitta_dut_parse_sensitivity_read_data(self, reg_addr: str, h_bit: int = 16, l_bit: int = 0):
        """
        数据解析函数，按照新的逻辑流程执行：
        1、进入循环模式
        2、
        1. 等待5秒后调用一次interact_with_sagitta_dut_read_data并记录数据
        2. 启动寄存器数据采集线程
        3. 等待15秒后再次调用interact_with_sagitta_dut_read_data
        4. 计算两次数据的PER值

        Args:
            reg_addr: 寄存器地址
            h_bit: 寄存器高位数
            l_bit: 寄存器低位数
        """
        default_rms_level = -60
        quick_step = 5
        slow_step = 1
        ref_per = 10

        # 获取初始per数据
        ret, result = self.sagitta_dut_parse_gauss_read_data(reg_addr="0xb40211a8", h_bit=16, l_bit=0)
        if ret is False:
            return False, result
        per = result["per"]

        while per < 10:
            cur_rms_level = default_rms_level
            self.cmw_api.cmw_device_gprf_generator_set_rms_level(cur_rms_level)

            ret, result = self.sagitta_dut_parse_gauss_read_data(reg_addr="0xb40211a8", h_bit=16, l_bit=0)
            if ret is False:
                return False, result
            per = result["per"]
            if per < 10:
                cur_rms_level = default_rms_level - quick_step

    def sagitta_dut_get_sensitivity_result(self, params: str):
        self.uart_handle.interact_with_sagitta_dut_end_test()
        time.sleep(0.5)

        self.uart_handle.interact_with_sagitta_dut_configure_test_params(params)
        time.sleep(0.5)

        ret, result = self.sagitta_dut_parse_sensitivity_read_data(reg_addr="0xb40211a8", h_bit=16, l_bit=0)
        if ret is False:
            return False, result
        else:
            return True, result
