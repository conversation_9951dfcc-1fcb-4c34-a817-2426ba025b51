# -*- coding: utf-8 -*-
import time
from gxapp.bttengine_run_data import EventEngine, Event, BttRunData
from module_set.device_run_func_class import DeviceRun
from module_set.handle_report_class import <PERSON>leExcelReport
from module_set.test_params import sagitta_symbol_rate_chg, sagitta_signal_channel_chg

class BaseBluetoothTest(DeviceRun):
    """蓝牙测试基类，提供通用功能"""
    
    def __init__(self, data: BttRunData, event_engine: EventEngine):
        super().__init__(data=data, event_engine=event_engine)
        self.event_engine = event_engine
        self.data = data
        self.rf_board_init_state = False
        self.select_cmw = True

        # 设备配置
        self.cmw_config = {
            "signal_path": "RF1O",
            "tx_path": "TX1",
            "baseband_mode": "ARB",
            "repetition_mode": "CONTinuous",
            "rms_level": -60,  # dBm
            "freq_offset": 0.007  # MHz
        }
        
        # 报告配置
        self.report_config = {
            "sheet_name_template": "{data_len}byte_{case_func}",
            "error_sheet_name": "Error_Log"
        }

    def initialize_test_environment(self):
        """初始化测试环境，包括设备连接和报告文件创建"""
        self.write_info_log("=== 初始化测试环境 ===")
        
        # 初始化设备和校验设备连接
        self.write_info_log("正在初始化设备连接...")
        ret, error = self.init_device_data_and_check_connection_correct()
        if not ret:
            self.write_error_log(f"设备初始化失败: {error}")
            return False, error

        # 创建报告文件
        self.write_info_log("正在创建测试报告...")
        ret, error, report_path_info = self.init_cur_testcase_report_file(cur_tc_file=__file__)
        if not ret:
            self.write_error_log(f"创建报告文件失败: {error}")
            return False, error
            
        self.tc_report_path = report_path_info["tc_report_path"]
        self.handle_report = HandleExcelReport(filename=self.tc_report_path)
        self.write_info_log(f"测试报告创建成功: {self.tc_report_path}")
        
        return True, None

    def cleanup_test_environment(self):
        """清理测试环境"""
        self.write_info_log("=== 清理测试环境 ===")
        try:
            self.disconnection_device()
            self.write_info_log("设备连接已断开")
            return True, None
        except Exception as e:
            error_msg = f"清理测试环境失败: {str(e)}"
            self.write_error_log(error_msg)
            return False, error_msg

    def setup_cmw_generator(self, test_fre):
        """配置CMW信号发生器参数"""
        self.write_info_log("开始配置CMW信号发生器...")
        try:
            self.cmw_api.cmw_func_gprf_generator_turn_off_status()
            self.cmw_api.cmw_device_gprf_generator_set_signal_path(
                self.cmw_config["signal_path"], 
                self.cmw_config["tx_path"]
            )
            self.cmw_api.cmw_device_gprf_generator_set_signal_baseband_mode(
                self.cmw_config["baseband_mode"]
            )
            self.cmw_api.cmw_device_gprf_generator_set_arb_file_repetition_mode(
                self.cmw_config["repetition_mode"]
            )
            self.cmw_api.cmw_device_gprf_generator_set_rms_level(
                self.cmw_config["rms_level"]
            )
            self.cmw_api.cmw_device_gprf_generator_set_unmod_rf_carrier_frequency(
                test_fre, "MHz"
            )
            self.write_info_log(f"CMW信号发生器配置完成，频率: {test_fre}MHz")
            return True, None
        except Exception as e:
            error_msg = f"CMW信号发生器配置失败: {str(e)}"
            self.write_error_log(error_msg)
            return False, error_msg

    def load_arb_file(self, test_params):
        """加载ARB文件并验证"""
        self.write_info_log("开始加载ARB文件...")
        try:
            ret, arb_file_path = self.cmw_api.cmw_func_gprf_generator_create_arb_file_path_from_params(
                sym_rate=test_params["sym_rate"],
                scode_rate=test_params["scode_rate"],
                snr=test_params["snr"],
                data_len=test_params["data_len"],
                test_func=self.case_func
            )
            
            if not ret:
                return False, f"创建ARB文件路径失败: {arb_file_path}"
                
            ret, error = self.cmw_api.cmw_func_query_gprf_generator_arb_file_is_exist(arb_file_path)
            if not ret:
                return False, f"ARB文件不存在: {error}"
                
            self.cmw_api.cmw_device_gprf_generator_set_arb_file(arb_file_path)
            loaded_file = self.cmw_api.cmw_device_gprf_generator_query_arb_file()
            self.write_info_log(f"ARB文件加载成功: {loaded_file}")
            
            return True, None
        except Exception as e:
            error_msg = f"加载ARB文件失败: {str(e)}"
            self.write_error_log(error_msg)
            return False, error_msg

    def validate_test_params(self, params):
        """验证测试参数的有效性"""
        try:
            for key, value in params.items():
                if value is None:
                    return False, f"参数 {key} 不能为空"
                if isinstance(value, (list, tuple)) and len(value) == 0:
                    return False, f"参数 {key} 不能为空列表"
            return True, None
        except Exception as e:
            return False, f"参数验证失败: {str(e)}"

    def create_gauss_testcases(self, test_params_config):
        """创建高斯测试用例列表
        
        Args:
            test_params_config (dict): 高斯测试参数配置，包含以下字段：
                - SYM_RATES: 符号率配置列表 [0, 1, 2]
                - S_CODE_EN: 编码使能配置列表 [0, 1, 2]
                - SCODE_RATES: 编码率配置列表
                - SNRS: 信噪比配置列表
                - DATA_LEN: 数据长度配置列表
                - MAX_TEST_CASES: 最大测试用例数
                
        Returns:
            list: 生成的测试用例列表，每个用例包含完整的测试参数
        """
        self.write_info_log("=== 生成高斯测试用例 ===")
        
        # 验证测试参数配置
        ret, error = self.validate_test_params(test_params_config)
        if not ret:
            self.write_error_log(f"测试参数配置无效: {error}")
            return []
            
        try:
            # 从配置中获取参数
            params = test_params_config
            self.write_info_log("高斯测试参数配置:")
            for key, value in params.items():
                if key != "MAX_TEST_CASES":
                    self.write_info_log(f"  {key}: {value}")
            
            # 生成测试用例
            testcase_list = []
            for dl in params["DATA_LEN"]:
                for sr in params["SYM_RATES"]:
                    for sce in params["S_CODE_EN"]:
                        for scr in params["SCODE_RATES"]:
                            for snr in params["SNRS"]:
                                # 获取对应的信号通道
                                channels = sagitta_signal_channel_chg[sagitta_symbol_rate_chg[sr]]
                                for ch in channels:
                                    testcase = {
                                        "data_len": dl,
                                        "sym_rate": sr,
                                        "s_code_en": sce,
                                        "scode_rate": scr,
                                        "snr": snr,
                                        "signal_ch": ch
                                    }
                                    testcase_list.append(testcase)
            
            # 限制测试用例数量
            max_cases = params["MAX_TEST_CASES"]
            if len(testcase_list) > max_cases:
                self.write_info_log(f"测试用例数量({len(testcase_list)})超过限制({max_cases})，将只执行前{max_cases}个用例")
                testcase_list = testcase_list[:max_cases]
            
            self.write_info_log(f"成功生成 {len(testcase_list)} 个高斯测试用例")
            return testcase_list
            
        except Exception as e:
            error_msg = f"生成高斯测试用例失败: {str(e)}"
            self.write_error_log(error_msg)
            return []

    def run(self):
        """主运行函数"""
        try:
            # 初始化测试环境
            ret, error = self.initialize_test_environment()
            if not ret:
                return ret, error

            # 执行测试流程
            self.edit_cur_testcase_test_process()

            # 清理测试环境
            ret, error = self.cleanup_test_environment()
            if not ret:
                return ret, error

            self.write_info_log("=== 测试完成 ===")
            return True, None
            
        except Exception as e:
            error_msg = f"测试执行过程中发生异常: {str(e)}"
            raise Exception(error_msg)
            # self.write_error_log(error_msg)
            # 确保在发生异常时也清理环境
            # self.cleanup_test_environment()
            # return False, error_msg

    def edit_cur_testcase_test_process(self):
        """主测试流程，需要子类实现"""
        raise NotImplementedError

    def run_single_test(self, tc, report_sheet_name):
        """执行单次测试，需要子类实现"""
        raise NotImplementedError 