import time

from module_set.device_base_api_class import DeviceBaseApi


class CmwApi(DeviceBaseApi):

    def __init__(self, ip: str, port: int):
        super().__init__(ip=ip, port=port)

    def cmw_device_hardcopy_set_screenshots_format(self, image_format: str):
        """
        指定通过 HCOPy:FILE、HCOPy:DATA?、HCOPy:INTerior:FILE 或 HCOPy:INTerior:DATA? 命令创建的截图格式。
            * full cmd format: ":HCOPy:DEVice:FORMat <image_format>"
            * short cmd format: ":HCOP:DEV:FORM <image_format>"
            * image_format:
                * BMP | JPG | PNG
            * Example:
                * HCOPy:DEVice:FORMat PNG
        """
        cmd = "HCOPy:DEVice:FORMat {}".format(image_format)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def cmw_device_hardcopy_query_captures_screenshot_block_data_format(self):
        """
        截图并以块数据格式返回结果，HCOPy:DATA 截取整个窗口，HCOPy:INTerior:DATA 只截取窗口内部。
        建议在发送此命令前 "打开 "显示屏，参见 SYSTem:DISPlay:UPDate。
            * full cmd format: ":HCOPy:DATA?"
            * short cmd format: ":HCOP:DATA?"
            * Return values:
                * dblock Data: Screenshot in 488.2 block data format
        """
        cmd = ":HCOPy:DATA?"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.1)
        data = self.recv_image_data()
        return data

    def cmw_device_system_display_remote_screen(self, status: str):
        """
        * Full cmd format: ":SYSTem:DISPlay:UPDate <status>"
        * Short cmd format: "SYST:DISP:UPD <status>"
        * status:
            * ON | 1: Display is shown and updated during remote control.
            * OFF | 0: Display shows static image during remote control.
        * 功能： 该命令用于控制是否在远程模式下更新显示。
        * 说明：
            * 默认情况下，仪器在远程模式下不更新显示。
        """
        cmd = "SYSTem:DISPlay:UPDate {}".format(status)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def cmw_device_system_reset_all(self):
        """
        * Full cmd format: "SYSTem:RESet:ALL"
        * Short cmd format: "SYST:RES:ALL"
        * PRESet 将所有子仪器的参数和基本设置设置为适合本地/手动交互的默认值。RESet 将它们设置为适合远程操作的默认值。
        """
        # cmd = "SYSTem:RESet:ALL"
        cmd = "*RST"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def cmw_device_system_reset_base(self):
        """
        * Full cmd format: "SYSTem:RESet:BASE"
        * Short cmd format: "SYST:RES:BASE"
        * 功能： PRESet 将基本设置设置为适合本地/手动交互的默认值。RESet 将它们设置为适合远程操作的默认值。
        """
        cmd = "SYSTem:RESet:BASE"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def cmw_device_system_reset_application(self, application: str):
        """
        * Full cmd format: "SYSTem:RESet:APPLication <application>"
        * Short cmd format: "SYST:RES:APPL <application>"
        * 功能： PRESet 命令将当前子仪器的参数设置为适合本地/手动交互的默认值。RESet 命令将当前子仪器的参数设置为适合远程操作的默认值。
        * application example (还有其他没有列举出，用到再补充):
            * Audio:
                * "Audio"
            * Bluetooth:
                * "Bluetooth Meas"
                * "Bluetooth Sig"
            * GPRF:
                * "GPRF Meas"
                * "GPRF Gen"
        """
        cmd = "SYSTem:RESet {}".format(application)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def cmw_device_query_gprf_generator_state(self, instance_numeric_suffix: int = None):
        """
        * Full cmd format: "SOURce:GPRF:GENerator<i>:STATe?"
        * Short cmd format: "SOUR:GPRF:GEN<i>:STAT?"
        * Query the output state of the generator.
        """
        if instance_numeric_suffix is None:
            cmd = "SOURce:GPRF:GENerator:STATe?"
        else:
            cmd = "SOURce:GPRF:GENerator{}:STATe?".format(instance_numeric_suffix)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.01)
        ret, data = self.recv_data()
        return data

    def cmw_device_set_gprf_generator_state(self, state: str or int):
        """
        * Full cmd format: "SOURce:GPRF:GENerator:STATe <state>"
        * Short cmd format: "SOUR:GPRF:GEN:STAT <state>"
        * state:
            * ON
            * OFF
        * 功能： 打开或关闭发生器。
        """
        cmd = "SOURce:GPRF:GENerator:STATe {}".format(state)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        # self.device_check_operation_complete()

    def cmw_device_gprf_generator_set_signal_path(self, tx_connector: str, tx_module:str):
        """
        * Full cmd format: "ROUTe:GPRF:GENerator:SCENario:SALone <tx_connector>, <tx_module>"
        * Short cmd format: "ROUT:GPRF:GEN:SCEN:SAL <tx_connector>, <tx_module>"
        * tx_connector:
            * "RF1O" | "RF3O" --> RF 1 OUT or RF 3 OUT
        * tx_module:
            * "TX1" | "TX2" | "TX3" | "TX4"
        * 功能： 设置 GPRF 生成器的信号路径。
        """
        cmd = "ROUTe:GPRF:GENerator:SCENario:SALone {},{}".format(tx_connector, tx_module)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def cmw_device_gprf_generator_set_signal_baseband_mode(self, baseband_mode: str):
        """
        * Full cmd format: "SOURce:GPRF:GENerator:BBMode <baseband_mode>"
        * Short cmd format: "SOUR:GPRF:GEN:BBM <baseband_mode>"
        * baseband_mode:
            * "CW" | "DTONe" | "ARB"
        * 功能： 设置 GPRF 生成器的基带模式。
        """
        cmd = "SOURce:GPRF:GENerator:BBMode {}".format(baseband_mode)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def cmw_device_gprf_generator_set_arb_file_repetition_mode(self, repetition_mode: str):
        """
        * Full cmd format: "SOURce:GPRF:GENerator:ARB:REPetition <repetition_mode>"
        * Short cmd format: "SOUR:GPRF:GEN:ARB:REP <repetition_mode>"
        * repetition_mode:
            * "CONTinuous" --> unlimited, cyclic processing
            * "SINGle" --> The file is processed n times, where n is the number of cycles, see SOURce:GPRF:GEN<i>:ARB:CYCLes <Cycles>
        * 功能： 设置 GPRF 生成器的 ARB 文件重复模式:
        """
        cmd = "SOURce:GPRF:GENerator:ARB:REPetition {}".format(repetition_mode)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def cmw_device_gprf_generator_set_arb_file_single_shot_repetition_cycles(self, cycles: int):
        """
        * Full cmd format: "SOURce:GPRF:GENerator:ARB:CYCles <cycles>"
        * Short cmd format: "SOUR:GPRF:GEN:ARB:CYC <cycles>"
        * cycles:
            * 1 ~ 10000
        * 功能： 设置 GPRF 生成器的 ARB 文件重复周期。
            * ARB 循环仅在单次重复模式下有效
        """
        cmd = "SOURce:GPRF:GENerator:ARB:CYCles {}".format(cycles)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def cmw_device_gprf_generator_set_arb_file_single_shot_repetition_additional_samples(self, add_samples: int):
        """
        * Full cmd format: "SOURce:GPRF:GENerator:ARB:ASAMples <add_samples>"
        * Short cmd format: "SOUR:GPRF:GEN:ARB:ASAM <add_samples>"
        * add_samples:
            * 0  to  max
            * max value depending on waveform file
        * 功能： 设置 GPRF 生成器的 ARB 文件通过指定数量的样本扩展波形文件的处理时间。
            * 附加样本仅在单次重复模式下有效
        """
        cmd = "SOURce:GPRF:GENerator:ARB:ASAMples {}".format(add_samples)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def cmw_device_gprf_generator_set_rms_level(self, level: int or float):
        """
        * Full cmd format: "SOURce:GPRF:GENerator:RFSettings:LEVel <level>"
        * Short cmd format: "SOUR:GPRF:GEN:RFS:LEV <level>"
        * level:
            * range: Please notice the ranges quoted in the data sheet
            * default unit: dBm
        * 功能： 设置 RF 发生器的基准 RMS 电平。
        """
        cmd = "SOURce:GPRF:GENerator:RFSettings:LEVel {}".format(level)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def cmw_device_gprf_generator_query_peak_envelope_power(self):
        """
        * Full cmd format: "SOURce:GPRF:GENerator:RFSettings:PEPower?"
        * Short cmd format: "SOUR:GPRF:GEN:RFS:PEP?"
        * 功能： 查询峰值包络功率。
        """
        cmd = "SOURce:GPRF:GENerator:RFSettings:PEPower?"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.01)
        ret, data = self.recv_data()
        return data

    def cmw_device_gprf_generator_set_unmod_rf_carrier_frequency(self, freq: int or float, unit: str = "Hz"):
        """
        * Full cmd format: "SOURce:GPRF:GENerator:RFSettings:FREQuency <freq><unit>"
        * Short cmd format: "SOUR:GPRF:GEN:RFS:FREQ <freq><unit>"
        * freq:
            * range: 100kHz ~ 6GHz
        * unit:
            * Hz | KHz | MHz | GHz
        * 功能： 设置未调制 RF 载波频率。
        """
        cmd = "SOURce:GPRF:GENerator:RFSettings:FREQuency {}{}".format(freq, unit)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def cmw_device_gprf_generator_set_arb_file(self, arb_file_abs_path: str):
        """
        * Full cmd format: "SOURce:GPRF:GENerator:ARB:FILE <arb_file_abs_path>"
        * Short cmd format: "SOUR:GPRF:GEN:ARB:FILE <arb_file_abs_path>"
        * arb_file_abs_path:
        * 功能： 为 ARB 基带模式选择波形文件。
        """
        cmd = "SOURce:GPRF:GENerator:ARB:FILE '{}'".format(arb_file_abs_path)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def cmw_device_gprf_generator_query_arb_file(self, path_type: str = None):
        """
        * Full cmd format: "SOURce:GPRF:GENerator:ARB:FILE:PATH? <path_type>"
        * Short cmd format: "SOUR:GPRF:GEN:ARB:FILE:PATH? <path_type>"
        * path_type:
        * 功能： 查询 ARB 文件的绝对路径。
            * 如果所选文件不存在或未选择任何文件，查询将返回“未选择任何文件”。
            * 如果所选文件存在，查询将返回：
                * 不带 <PathType>：用于选择文件的字符串。如果已使用别名，则不会替换别名。
                * 带 <PathType>：文件的绝对路径。如果已使用别名，则将替换别名。
        """
        if path_type is None:
            cmd = "SOURce:GPRF:GENerator:ARB:FILE?"
        else:
            cmd = "SOURce:GPRF:GENerator:ARB:FILE? {}".format(path_type)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.01)
        ret, data = self.recv_data()
        return data

    def cmw_device_query_specify_dir_contents(self, specify_dir: str):
        """
        * Full cmd format: "MMEMory:CATalog? <specify_dir>"
        * Short cmd format: "MMEM:CAT? <specify_dir>"
        * specify_dir:
            * "C:\\Users\\<USER>\\Documents\\RohdeSchwarz\\NRP-Z\\ARB"
        * 功能： 查询指定目录的内容。
        """
        cmd = "MMEMory:CATalog? '{}'".format(specify_dir)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.01)
        ret, data = self.recv_data()
        print(data)
        return data
