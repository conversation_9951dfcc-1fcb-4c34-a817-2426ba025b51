
import serial
import re
import time
import threading
from datetime import datetime

from gxpy.common.gxmessagepack import GxMessageData, GxMessagePack


class SerialPortCommunication(object):

    def __init__(self, port: str, baudrate: int):
        self.port = port
        self.baudrate = baudrate
        self.uart_handle = serial.Serial(port=self.port, baudrate=self.baudrate, timeout=0.1)
        self.msg_pack = GxMessagePack()
        self.serial_lock = threading.Lock()  # 串口访问锁，防止并发访问

    def close(self):
        """关闭串口连接"""
        self.uart_handle.close()

    def sagitta_test_send_msg(self, msg: str):
        data = msg.encode("utf-8")
        print("[{}] send: {}".format(datetime.now(), data))
        try:
            self.uart_handle.write(data)
        except Exception as e:
            return False
        return True

    def sagitta_test_receive_msg(self):
        receive_buffer = list()
        while True:
            try:
                # data = self.uart_handle.read(1024)
                data = self.uart_handle.readlines()
                if not data:
                    break
            except Exception as e:
                error = str(e)
                return False, error

            if len(data) == 0:
                break
            else:
                try:
                    for d in data:
                        receive_buffer.append(re.sub(r"\r\n", "", d.decode("utf-8")))
                except Exception as e:
                    print(str(e))
                    return False

        if receive_buffer == list():
            return False
        else:
            # print("{}, {}".format(len(receive_buffer), receive_buffer))
            return receive_buffer

    def sagitta_send_and_receive(self, msg: str, wait_time: float = 0.005):
        """
        线程安全的发送和接收方法，防止并发访问串口

        Args:
            msg: 要发送的消息
            wait_time: 发送后等待时间（秒）

        Returns:
            tuple: (success, data) - success为布尔值，data为接收到的数据或错误信息
        """
        with self.serial_lock:
            # 发送消息
            ret = self.sagitta_test_send_msg(msg)
            if ret is False:
                return False, "send cmd failed! cmd_info: {}".format(msg)

            # 等待一小段时间
            time.sleep(wait_time)

            # 接收消息
            rsv_data = self.sagitta_test_receive_msg()
            if rsv_data is False:
                return False, "rsv data failed! cmd_info: {}".format(msg)
            elif isinstance(rsv_data, tuple) and rsv_data[0] is False:
                # 处理接收错误的情况
                return False, "rsv data error: {}".format(rsv_data[1])
            else:
                return True, rsv_data

    def send_msg(self, msg):
        data = self.msg_pack.msg_data_pack(msg=msg)
        if data is not None:
            try:
                self.uart_handle.write(data)
            except Exception as e:
                error = str(e)
                return False, error
        else:
            error = "send msg data pack error"
            return False, error
        return True, None

    def receive_msg(self):
        receive_buffer = bytearray(0)
        while True:
            try:
                data = self.uart_handle.read(1024)
                if not data:
                    break
                receive_buffer += data
            except Exception as e:
                error = str(e)
                return False, error

        index = self.msg_pack.msg_data_find_next_magic(receive_buffer, 1)
        if index == -1:
            error = "magic not in receive buffer data"
            return False, error
        else:
            receive_buffer = receive_buffer[index:]
        ret, msg = self.msg_pack.msg_data_unpack(receive_buffer)
        if ret is False:
            error = "recv msg data unpack error"
            return False, error
        else:
            if ret is True and msg is not None:
                return True, msg
            else:
                error = "recv msg data length error"
                return False, error

    def check_for_correct_reg_addr(self, reg_addr: int or str, rw_flag: str):
        """
        检查寄存器地址是否正确，为了统一，寄存器地址只能为10位带前缀的16进制字符串
        * reg_addr:
            * str: fmt: "0x01000001"
        * rw_flag:
            * 'r' : 'read'
            * 'w' : 'write'
        """
        rw_chg = {'r' : 'read', 'w' : 'write'}
        if isinstance(reg_addr, str):
            # 判断寄存器地址长度是否是10位
            len_reg_addr = len(reg_addr)
            if len_reg_addr > 10 or len_reg_addr < 10:
                error = "{} reg_addr: 寄存器地址的长度错误, 请按格式输入长度为10位的16进制字符串: eg: '0x0100003C'".format(rw_chg[rw_flag])
                return False, error

            # 判断寄存器地址格式是否正确
            if not (reg_addr.startswith('0x') or reg_addr.startswith('0X')):
                error = "{} reg_addr: 寄存器地址的前缀格式错误, 16进制字符串以'0x'或'0X'开头: eg: '0x0100003C'".format(rw_chg[rw_flag])
                return False, error

            # 判断寄存器地址数值是否正确
            try:
                str_reg_addr = "{:#010x}".format(eval(reg_addr))
                return True, str_reg_addr
            except Exception as e:
                error = "{} reg_addr: 寄存器地址的数值错误, 请输入10位的符合16进制格式的字符串，格式为：'0x' + '十六进制数字(0-9以及A-F或a-f)': eg: '0x0100003C'".format(rw_chg[rw_flag])
                return False, error
        else:
            error = "{} reg_addr: 寄存器地址的类型错误，请按格式输入10位的16进制字符串: eg: '0x0100003C'".format(rw_chg[rw_flag])
            return False, error

    def check_for_correct_reg_val(self, reg_val: str):
        """
        检查更改的寄存器值是否正确，为了统一，寄存器地址只能为3位或4位带前缀的16进制字符串
        * reg_addr:
            * str: fmt: "0x01" or "0x1"
        """
        if isinstance(reg_val, str):
            # 判断寄存器值长度是否是10位
            len_reg_val = len(reg_val)
            if len_reg_val >= 5 or len_reg_val <= 2:
                error = "reg_val: 寄存器值的长度错误, 请按格式输入长度为3位或4位的16进制字符串: eg: '0x01'或者'0x1'"
                return False, error

            # 判断寄存器值格式是否正确
            if not (reg_val.startswith('0x') or reg_val.startswith('0X')):
                error = "reg_addr: 寄存器值的前缀格式错误, 16进制字符串以'0x'或'0X'开头: eg: '0x01'或者'0x1'"
                return False, error

            # 判断寄存器值的数值是否正确
            try:
                str_reg_val = "{:#04x}".format(eval(reg_val))
                int_reg_val = int(str_reg_val, 16)
                return True, int_reg_val
            except Exception as e:
                error = "reg_addr: 寄存器值的数值错误, 请输入长度为3位或4位的符合16进制格式的字符串，格式为：'0x' + '十六进制数字(0-9以及A-F或a-f)': eg: '0x01'或者'0x1'"
                return False, error
        else:
            error = "reg_addr: 寄存器值的类型错误，请按格式输入长度为3位或4位的16进制字符串: eg: '0x01'或者'0x1'"
            return False, error

    def write_register_data(self, reg_addr: str, reg_val: str, is_crc: bool = False):
        """
        * reg_addr:
            * str: "0x01000001"
        * reg_val:
            * str: "0x01"
        * is_crc:
            * False: 消息体不做crc校验
            * True:  消息体做crc校验
        """
        ret, data = self.check_for_correct_reg_addr(reg_addr=reg_addr, rw_flag="w")
        if ret is False:
            error = data
            return ret, error
        else:
            str_reg_addr = data

        ret, data = self.check_for_correct_reg_val(reg_val=reg_val)
        if ret is False:
            error = data
            return ret, error
        else:
            reg_val = data

        msg_cmd = 0x0003
        msg_type = 0x01
        msg_seq = 0x00
        if is_crc:
            msg_flags = 0x01
        else:
            msg_flags = 0x00
        msg_content = bytearray(8)
        msg_content[0] = int(str_reg_addr[0:4], 16)
        msg_content[3] = int(str_reg_addr[8:11], 16)
        msg_content[7] = reg_val

        # 组合数据并发送（数据在self.send_msg中进行pack）
        msg_write_data = GxMessageData(msg_cmd, msg_type, msg_seq, msg_flags, msg_content)
        ret, error = self.send_msg(msg=msg_write_data)
        if ret is False:
            return ret, error
        time.sleep(5)

        # 获取返回数据（数据在self.receive_msg中进行unpack）
        ret, msg = self.receive_msg()
        if ret is False:
            error = msg
            return ret, error
        else:
            msg_recv_data = msg

        # 检验返回数据
        if msg_recv_data.cmd != 0x0004:     # 写回复命令是0x0004
            error = "write register data: recv data cmd val error"
            return False, error
        if msg_recv_data.type != 0x2:
            error = "write register data: recv data cmd type error"
            return False, error
        if (msg_recv_data.content[0] != msg_content[0]
                or msg_recv_data.content[3] != msg_content[3]):
            error = "write register data: recv data reg addr error"
            return False, error
        if msg_recv_data.content[7] != msg_content[7]:
            error = "write register data: recv data reg addr val error"
            return False, error

        return True, msg_recv_data.content[7]

    def read_register_data(self, reg_addr: int or str, is_crc: bool = False):
        """
        * reg_addr:
            * str: "0x01000001"
        * is_crc:
            * False: 消息体不做crc校验
            * True:  消息体做crc校验
        """
        ret, data = self.check_for_correct_reg_addr(reg_addr=reg_addr, rw_flag="r")
        if ret is False:
            error = data
            return ret, error
        else:
            str_reg_addr = data

        msg_cmd = 0x0001
        msg_type = 0x01
        msg_seq = 0x00
        if is_crc:
            msg_flags = 0x01
        else:
            msg_flags = 0x00
        msg_content = bytearray(8)
        msg_content[0] = int(str_reg_addr[0:4], 16)
        msg_content[3] = int(str_reg_addr[8:11], 16)
        msg_content[7] = 0x01   # 读取寄存器地址的值，读取一个字节的内容

        # 组合数据并发送（数据在self.send_msg中进行pack）
        msg_read_data = GxMessageData(msg_cmd, msg_type, msg_seq, msg_flags, msg_content)
        ret, error = self.send_msg(msg=msg_read_data)
        if ret is False:
            return ret, error
        time.sleep(5)

        # 获取返回数据（数据在self.receive_msg中进行unpack）
        ret, msg = self.receive_msg()
        if ret is False:
            error = msg
            return ret, error
        else:
            msg_recv_data = msg

        # 检验返回数据
        if msg_recv_data.cmd != 0x0002:     # 读回复命令是0x0002
            error = "read register data: recv data cmd val error"
            return False, error
        if msg_recv_data.type != 0x2:       # 下位机发送给主机用
            error = "read register data: recv data cmd type error"
            return False, error
        if (msg_recv_data.content[0] != msg_content[0]
                or msg_recv_data.content[3] != msg_content[3]):
            error = "read register data: recv data reg addr error"
            return False, error
        # 读取指定寄存器地址的value
        read_reg_addr_val = msg_recv_data.content[7]

        return True, read_reg_addr_val

    def send_serial_msg(self, data):
        data = data.encode("utf-8")
        print(data)
        try:
            self.uart_handle.write(data)
        except Exception as e:
            return False
        return True

    def receive_serial_msg(self):
        """
            receive data:
                * [b'code:0 message: reply set_demod_type successful\r\n']
            return data:
                * 'code:0 message: reply set_demod_type successful'
        """

        timeout = 20
        receive_buffer = ""
        while timeout > 0:
            timeout -= 1
            try:
                data = self.uart_handle.readlines()
                if len(data) > 0:
                    print(data)
            except Exception as e:
                continue

            if len(data) == 1:
                receive_buffer = data[-1]
            elif len(data) > 1:
                line_1 = data[-1]
                line_2 = data[-2]
                if b'code:' in line_1:
                    receive_buffer = line_1
                else:
                    if b'code:' in line_2:
                        receive_buffer = line_2 + line_1
                    else:
                        continue
            else:
                continue
            try:
                receive_buffer = receive_buffer.decode("utf-8")
            except Exception as e:
                print(str(e))
                return False

            buf_len = len(receive_buffer)
            if buf_len > 2 and receive_buffer[-2:] == '\r\n':
                break
        if receive_buffer == "":
            return False
        else:
            receive_buffer = re.sub(r'\r\n|\r|\n', '', receive_buffer)  # 去除\r\n
            # self.write_debug_log(msg="返回数据为：{}".format(receive_buffer))
            return receive_buffer

    def __compare_rsv_msg_and_kws(self, rsv_data: str, kws: str):
        """"""
        if rsv_data.find(kws) == -1:    # kws not in rsv_data
            return False
        else:
            return True

    def interact_with_stb_set_demod_type(self, mod_type: str):
        """
        Command eg:
            set_demod_type type=dvbs\r\n
            set_demod_type type=dvbt2\r\n
        """
        timeout = 5
        compare_kws = "code:0"
        if mod_type in ["DVBT2", "DVBT", "T2MI"]:
            type_info = "dvbt2"
        elif mod_type in ["DTMB"]:
            type_info = "dvbdtmb"
        elif mod_type in ["DVBS2X", "DVBS2", "DVBS", "S2L3"]:
            type_info = "dvbs"
        elif mod_type in ["DVBC", "J83B"]:
            type_info = "dvbc"
        elif mod_type in ["ARB"]:
            type_info = "abss"
        else:
            return False
        cmd = "set_demod_type type={}\r\n".format(type_info)
        for i in range(timeout):
            ret = self.send_serial_msg(data=cmd)
            if ret is False:
                continue
            rsv_data = self.receive_serial_msg()
            if rsv_data is False:
                continue
            else:
                ret = self.__compare_rsv_msg_and_kws(rsv_data=rsv_data, kws=compare_kws)
                if ret is True:
                    return True
        return False

    def interact_with_stb_set_tp_dvbs(self, frequency: int, symrate: int):
        """
        Pars:
            * frequency -> 1050000000(Hz)
            * symrate -> 27500000(bd)
        Command: FOR DVBS、DVBS2、DVBS2X、S2L3
            * frequency -> 1030000(KHz)
            * symrate -> 27500000(bd)
            * dmx：使用的DEMUX设备，默认这个字段可以不填
            * tsmode：0，TS并行连接，1，TS串行连接；
            * tssource：TS输入源；
            * tsselect：tuner号
        Command eg:
            Full command:   set_tp fre=1030000 sym=27500000 dmx=0 tsmode=0 tssource=0 tsselect=0 pol=0\r\n
            Simple command: set_tp fre=1030000 sym=27500000\r\n
        """
        timeout = 1
        compare_kws = "code:0"
        frequency = int(frequency / 1000)
        cmd = "set_tp fre={} sym={}\r\n".format(frequency, symrate)
        for i in range(timeout):
            ret = self.send_serial_msg(data=cmd)
            if ret is False:
                continue
            rsv_data = self.receive_serial_msg()
            if rsv_data is False:
                continue
            else:
                ret = self.__compare_rsv_msg_and_kws(rsv_data=rsv_data, kws=compare_kws)
                if ret is True:
                    return True
        return False

    def interact_with_stb_get_tp_status(self):
        """
        Command: eg: get_tp_status\r\n
        """
        timeout = 30
        compare_kws = "code:0 data: (1)"
        cmd = "get_tp_status\r\n"
        for i in range(timeout):
            ret = self.send_serial_msg(data=cmd)
            if ret is False:
                continue
            time.sleep(0.1)
            rsv_data = self.receive_serial_msg()
            if rsv_data is False:
                continue
            else:
                ret = self.__compare_rsv_msg_and_kws(rsv_data=rsv_data, kws=compare_kws)
                if ret is True:
                    return True
        return False

    def interact_with_stb_set_play_parameter(self, v_pid: int, a_pid: int, p_pid: int, v_fmt: int, a_fmt: int):
        """
        Pars:
            * v_pid -> video pid
            * a_pid -> audio pid
            * p_pid -> pcr pid
            * v_fmt -> 视频的编码类型
                * 0 -> mpeg1/2
                * 2 -> mpeg4/h.264
            * a_fmt -> 音频的解码器类型
                * 0 -> mpeg1/2
                * 2 -> dobly
        Command:
            eg: play vpid=36 apid=35 ppid=36 vformat=0 aformat=0\r\n
        """
        timeout = 5
        compare_kws = "code:0"
        if v_fmt not in [0, 2]:
            return False
        if a_fmt not in [0, 2]:
            return False
        cmd = "play vpid={} apid={} ppid={} vformat={} aformat={}\r\n".format(v_pid, a_pid, p_pid, v_fmt, a_fmt)
        for i in range(timeout):
            ret = self.send_serial_msg(data=cmd)
            if ret is False:
                continue
            rsv_data = self.receive_serial_msg()
            if rsv_data is False:
                continue
            else:
                ret = self.__compare_rsv_msg_and_kws(rsv_data=rsv_data, kws=compare_kws)
                if ret is True:
                    return True
        return False

    def interact_with_stb_set_ts_compare_tspid(self, tspid: int):
        """
        Pars:
            * tspid -> ts file pid
        Command:
            eg: set_ts_compare tspid=36\r\n
        """
        timeout = 5
        compare_kws = "code:0"
        cmd = "set_ts_compare tspid={}\r\n".format(tspid)
        for i in range(timeout):
            ret = self.send_serial_msg(data=cmd)
            if ret is False:
                continue
            rsv_data = self.receive_serial_msg()
            if rsv_data is False:
                continue
            else:
                ret = self.__compare_rsv_msg_and_kws(rsv_data=rsv_data, kws=compare_kws)
                if ret is True:
                    return True
        return False

    def interact_with_stb_set_exit(self):
        """
        Command: eg: exit\r\n
        """
        timeout = 5
        compare_kws = "code:0"
        cmd = "exit\r\n"
        for i in range(timeout):
            ret = self.send_serial_msg(data=cmd)
            if ret is False:
                continue
            rsv_data = self.receive_serial_msg()
            if rsv_data is False:
                continue
            else:
                ret = self.__compare_rsv_msg_and_kws(rsv_data=rsv_data, kws=compare_kws)
                if ret is True:
                    self.__stb_set_demod_status = False
                    return True
        return False

    def handle_sagitta_dut_response(self, rsv_data: list, compare_kws: str):
        for d in rsv_data:
            if compare_kws in d:
                print("compare_data: {}".format(d))
                return True, d
        return False, None

    def interact_with_sagitta_dut_init_device(self):
        """
        Command: eg: bb run 66\r\n
        """
        timeout = 1
        compare_kws = "bdaddr:"
        cmd = "bb run 66\r\n"
        for i in range(timeout):
            # 使用线程安全的发送和接收方法
            ret, rsv_data = self.sagitta_send_and_receive(cmd)
            if ret is False:
                continue
            else:
                print("rsv_data: {}".format(rsv_data))
                ret, d = self.handle_sagitta_dut_response(rsv_data=rsv_data, compare_kws=compare_kws)
                if ret is False:
                    continue
                ret = self.__compare_rsv_msg_and_kws(d, compare_kws)
                if ret:
                    return True
        return False

    def interact_with_sagitta_dut_configure_test_params(self, params: str):
        """
        Command: eg: bb test_cmw 0 0 1 4 6\r\n
        * cmd_format: bb test_cmw chnl symbol_rate s_mode s_rate len\r\n
            * param:
                * chnl: 十进制信道号
                * symbol_rate: 0-1M  1-2M  2-4M
                * s_mode: 0-bypass, 1-scode_en_with_crc8, 2-scode_en_without_crc8
                * s_rate: 2~7，对应 2/8~7/8 码率
                * len: 数据长度，单位为 byte
        """
        timeout = 1
        compare_kws = "bb test_cmw channel"
        cmd = "{}\r\n".format(params)
        for i in range(timeout):
            # 使用线程安全的发送和接收方法
            ret, rsv_data = self.sagitta_send_and_receive(cmd)
            if ret is False:
                continue
            else:
                print("rsv_data: {}".format(rsv_data))
                ret, d = self.handle_sagitta_dut_response(rsv_data=rsv_data, compare_kws=compare_kws)
                if ret is False:
                    continue
                ret = self.__compare_rsv_msg_and_kws(d, compare_kws)
                if ret:
                    return True
        return False

    def parse_sagitta_dut_rd_data(self, data: str):
        rsv_kws = "test_rd"
        result = dict()

        # 分割数据并过滤空字符串
        split_data = list(filter(None, re.split(rsv_kws, data)))

        # 检查分割后的数据是否有效
        if len(split_data) == 0:
            # 如果没有找到分割点，直接使用原始数据
            valid_data = data
        elif len(split_data) == 1:
            # 如果只有一个部分，使用第一个部分
            valid_data = split_data[0]
        else:
            # 如果有多个部分，使用第二个部分（索引1）
            valid_data = split_data[1]

        valid_data_list = list(filter(None, re.split(r"\s|,|，", valid_data)))
        for d in valid_data_list:
            if "rx_ok" in d:
                result["rx_ok"] = int(d.split("=")[1])
            elif "tx" in d:
                result["tx"] = int(d.split("=")[1])
            elif "crc_err" in d:
                result["crc_err"] = int(d.split("=")[1])
            elif "len_err" in d:
                result["len_err"] = int(d.split("=")[1])
            elif "sync_err" in d:
                result["sync_err"] = int(d.split("=")[1])
        return result

    def interact_with_sagitta_dut_read_data(self):
        """
        Command: eg: bb test_rd\r\n
        """
        error = None
        timeout = 1
        compare_kws_1 = "test_rd rx_ok"
        compare_kws_2 = "test_rd, cmd_status="
        cmd = "bb test_rd\r\n"
        for i in range(timeout):
            # 使用线程安全的发送和接收方法
            ret, rsv_data = self.sagitta_send_and_receive(cmd)
            if ret is False:
                error = rsv_data  # rsv_data 在这里是错误信息
                continue
            else:
                print("rsv_data: {}".format(rsv_data))
                ret_1, rd_data = self.handle_sagitta_dut_response(rsv_data=rsv_data, compare_kws=compare_kws_1)
                if ret_1 is False:
                    error = "rsv data err! rd_data not in rsv_data: {}".format(rsv_data)
                    continue

                ret_2, rd_state_data = self.handle_sagitta_dut_response(rsv_data=rsv_data, compare_kws=compare_kws_2)
                if ret_2 is False:
                    error = "rsv data err! rd_state_data not in rsv_data: {}".format(rsv_data)
                    continue

                rd_state = re.split(r"=", rd_state_data)[1]
                if rd_state != '0':
                    error = "rd data cmd status err: {}".format(rd_state_data)
                    continue

                try:
                    result = self.parse_sagitta_dut_rd_data(data=rd_data)
                    return True, result
                except Exception as e:
                    error = "parse rsv data err: {}".format(e)

        return False, error

    def interact_with_sagitta_dut_end_test(self):
        """
        Command: eg: bb test_end\r\n
        """
        error = None
        timeout = 1
        compare_kws = "test_end, cmd_status="
        cmd = "bb test_end\r\n"
        for i in range(timeout):
            # 使用线程安全的发送和接收方法
            ret, rsv_data = self.sagitta_send_and_receive(cmd)
            if ret is False:
                error = rsv_data  # rsv_data 在这里是错误信息
                continue
            else:
                print("rsv_data: {}".format(rsv_data))
                ret, test_end_rsv_data = self.handle_sagitta_dut_response(rsv_data=rsv_data, compare_kws=compare_kws)
                if ret is False:
                    error = "rsv data err! test_end_rsv_data not in rsv_data: {}".format(rsv_data)
                    continue

                end_state = re.split(r"=", test_end_rsv_data)[1]
                if end_state != '0':
                    error = "test end status err: {}".format(test_end_rsv_data)
                    continue
                else:
                    return True, None

        return False, error

    def interact_with_sagitta_dut_get_register_data(self, reg_addr: str, h_bit: int, l_bit: int):
        """
        Command: eg: bb rd 0xb40211a8 16 0\r\n
        * cmd_format: bb rd reg_addr h_bit l_bit\r\n
            * param:
                * reg_addr: 寄存器地址
                * h_bit: 高位数
                * l_bit: 低位数
        """
        error = None
        timeout = 1
        compare_kws = "val-"
        cmd = "bb rd {reg_addr} {h_bit} {l_bit}\r\n".format(reg_addr=reg_addr, h_bit=h_bit, l_bit=l_bit)
        for i in range(timeout):
            # 使用线程安全的发送和接收方法
            ret, rsv_data = self.sagitta_send_and_receive(cmd)
            if ret is False:
                error = rsv_data  # rsv_data 在这里是错误信息
                continue
            else:
                print("rsv_data: {}".format(rsv_data))
                ret, rd_reg_data = self.handle_sagitta_dut_response(rsv_data=rsv_data, compare_kws=compare_kws)
                if ret is False:
                    error = "rsv data err! rd_reg_data not in rsv_data: {}".format(rsv_data)
                    continue

                reg_data = int(re.split(r"-", rd_reg_data)[1])
                polar_err = "{:.4e}".format(float(int(reg_data) / (16*512)))
                return True, polar_err

        return False, error
