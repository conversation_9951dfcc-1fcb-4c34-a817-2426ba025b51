import platform
import re
import time
import os
import io

from PIL import Image

from module_set.cmw_device_api import CmwApi
from module_set.test_params import *


class CmwFunc(CmwApi):

    def __init__(self, ip: str, port: int):
        super().__init__(ip=ip, port=port)

    @staticmethod
    def cmw_func_handle_block_data_format(data: bytes) -> bytes:
        """
        块数据是一种适用于传输大量数据的传输格式。
        * 二进制数据块的结构如下： #<number><length entry><block data>
            * #:
                * '#'在二进制数据块中总是排在第一位
            * <number>:
                * 表示后面的长度条目有多少位数字
            * <length entry>:
                * 表示随后的字节数
            * <block data>:
                * 指定长度的块数据
        * Example:
            * data: #542538xxxxxxxxx......
            * data的数据规律如下：
                * #： 第一位，hash symbol，块数据总是以‘#’开头
                * 5： 第二位，表示随后的5个数字（示例中指的是42538，其长度为5）
                * 42538：表示之后的数据（xxxxxxxxx......）的长度
                * xxxxxxxxx......: 图片数据
        """
        # 取第二位（eg: 542538），5：表示随后的5个数字（这里例子中指的是42538，其长度为5)
        second_numb = int(chr(data[1]))
        # 42538：表示之后的数据（xxxxxxxxxxxxx……）的长度
        db_length = ''
        for i in range(second_numb):
            db_length += chr(data[i + 2])
        db_length = int(db_length)
        # xxxxx……：图片数据
        image_data = data[(2 + second_numb): (2 + second_numb) + db_length]
        return image_data

    def cmw_func_save_screenshot_image(self, save_report_path: str or None, save_image_path: str, image_name: str):
        """
        对设备进行截图并传输到上位机保存
            * save_report_path: 报告保存路径
            * save_image_path: 图片保存路径
            * image_name: 图片保存名词
        """
        if save_report_path is None:
            if not os.path.exists(save_image_path):
                os.mkdir(save_image_path)
            image_file_path = os.path.join(save_image_path, image_name)
        else:
            image_save_path = os.path.join(save_report_path, save_image_path)
            if not os.path.exists(image_save_path):
                os.mkdir(image_save_path)
            image_file_path = os.path.join(image_save_path, image_name)
        block_data = self.cmw_device_hardcopy_query_captures_screenshot_block_data_format()
        image_data = self.cmw_func_handle_block_data_format(data=block_data)
        stream = io.BytesIO(image_data)
        image = Image.open(stream)
        image.save(image_file_path)

    def cmw_func_gprf_generator_turn_on_status_wait_load_arb_file(self):
        """
        将 GPRF 生成器设置为 ON 状态时，等待 ARB 文件加载完成。
        """
        while True:
            state = self.cmw_device_query_gprf_generator_state()
            if state == "ON":
                break
            elif state == "OFF":
                return False, "generator state is OFF"
            time.sleep(5)
        return True, None

    def cmw_func_gprf_generator_create_arb_file_path_from_params(
            self, sym_rate: int, scode_rate: float, snr: int, data_len: int, test_func: str):
        """
        根据参数生成 ARB 文件路径
        * sym_rate: 0-1M  1-2M  2-4M
        * scode_rate: 2-2/8  3-3/8  4-4/8  5-5/8  6-6/8  7-7/8
        * snr: 1-0dB  2-1dB  3-2dB  4-3dB  5-4dB  6-5dB
        * data_len: 数据长度，单位为 byte
        * test_func: 测试功能，eg: Gauss
        """
        error = None
        if sym_rate not in sagitta_symbol_rate_chg.keys():
            error = "sym_rate 参数错误"
            return False, error
        if scode_rate not in sagitta_scode_rate_chg.keys():
            error = "scode_rate 参数错误"
            return False, error
        if snr not in arb_file_snr_chg.keys():
            error = "snr_code 参数错误"
            return False, error

        cur_sym_rate = sagitta_symbol_rate_chg[sym_rate]
        cur_scode_rate = sagitta_scode_rate_chg[scode_rate]
        cur_snr = arb_file_snr_chg[snr]

        if test_func == "Gauss":
            fix_path = os.path.join("D:", "sle_stream", "test")
            first_path = r"{}byte_{}".format(data_len, test_func)
            second_path = r"{}byte_10000".format(data_len)
            third_path = "R{}polar{}".format(
                cur_sym_rate, arb_file_str_scode_rate[cur_scode_rate])
            file_name = "qpsk_{}Rs_24Fs_{}fps_PType{}_seed0_{}bk_sym{}db_grdc.wv".format(
                cur_sym_rate, arb_file_fps[cur_sym_rate], arb_file_ptype[cur_scode_rate],
                arb_file_bk[cur_scode_rate], cur_snr)
            arb_file_path = os.path.join(fix_path, first_path, second_path, third_path, file_name)
            print(arb_file_path)
            if platform.system() in ["Linux", "Darwin"]:
                arb_file_path = arb_file_path.replace("/", "\\")
            return True, arb_file_path
        else:
            error = "test_func 参数错误"
            return False, error

    def cmw_func_query_gprf_generator_arb_file_is_exist(self, arb_file_path: str):
        """
        查询 ARB 文件是否存在
        * arb_file_path:
            * eg: r"D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar4_8\qpsk_1Rs_24Fs_1000fps_PType2_seed0_4bk_sym2db_grdc.wv"
        """
        wv_file_list = list()
        if platform.system() in ["Linux", "Darwin"]:
            arb_file_path = arb_file_path.replace("\\", "/")

        arb_file_name = os.path.basename(arb_file_path)
        ret = self.cmw_device_query_specify_dir_contents(specify_dir=os.path.dirname(arb_file_path))
        if len(ret) > 0:
            for i in re.split(r'[,"]', ret):
                if i.endswith(".wv"):
                    wv_file_list.append(i)

        print(wv_file_list)
        if arb_file_name in wv_file_list:
            return True, None
        else:
            error = "ARB 文件不存在"
            return False, error
