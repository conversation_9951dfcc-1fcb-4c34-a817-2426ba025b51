import socket
import re
import os
import serial
import serial.tools.list_ports
import logging

from datetime import datetime
from module_set.vsg_device_func import VsgFunc
from module_set.sa_device_func import SaFunc
from module_set.dcp_device_func import DcPowerFunc
from module_set.cmw_device_func import CmwFunc
from module_set.serial_base_class import SerialPortCommunication

from gxapp.logengine import LogData, EVENT_LOG, EVENT_LOG_SET_DIR, LogDirData
from gxapp.bttengine_run_data import EventEngine, Event, BttRunData
from gxapp.public_data import device_model


class DeviceRun:

    def __init__(self, data: BttRunData, event_engine: EventEngine):
        self.event_engine = event_engine
        self.data = data
        self.tc_report_path = None
        self.tc_report_dir_path = None
        self.handle_report = None

        self.sa_ip = self.data["spectrum_analyzer"]
        self.vsg_ip = self.data["signal_generator"]
        self.cmw_ip = self.data["bluetooth_tester"]
        self.dcp_ip = self.data["dc_power"]
        self.uart_port = self.data["uart_port"]
        self.line_loss = self.data["line_loss"]
        self.save_report_path = self.data["save_report_path"]

        self.select_sa = False
        self.select_vsg = False
        self.select_cmw = False
        self.select_dcp = False

        self.sa_api = None
        self.vsg_api = None
        self.cmw_api = None
        self.dcp_api = None
        self.uart_handle = None

        self.socket_port = 5025
        self.baudrate = 115200
        self.device_api = dict()

    def write_error_log(self, msg: str):
        """
        Put log event with specific message.
        """
        print("Error Log: {}".format(msg))
        log = LogData(msg=msg, level=logging.ERROR)
        event = Event(EVENT_LOG, log)
        self.event_engine.put(event)

    def write_info_log(self, msg: str):
        """
        Put log event with specific message.
        """
        print("Info Log: {}".format(msg))
        log = LogData(msg=msg, level=logging.INFO)
        event = Event(EVENT_LOG, log)
        self.event_engine.put(event)

    def write_debug_log(self, msg: str):
        """
        Put log event with specific message.
        """
        print("Debug Log: {}".format(msg))
        log = LogData(msg=msg, level=logging.DEBUG)
        event = Event(EVENT_LOG, log)
        self.event_engine.put(event)

    def init_device_data_and_check_connection_correct(self):
        # 初始化设备连接
        ret, error = self.init_device_api()
        if ret is False:
            return False, error
        # 检查设备连接正确性
        ret, error = self.check_device_connection_correct()
        if ret is False:
            return False, error
        # 初始化串口连接
        ret, error = self.init_uart()
        if ret is False:
            return False, error
        return True, None

    def init_uart(self):
        for n, (portname, desc, hwid) in enumerate(sorted(serial.tools.list_ports.comports())):
            print(portname, desc, hwid)

        if self.uart_handle:
            self.uart_handle.close()
        try:
            self.uart_handle = SerialPortCommunication(port=self.uart_port, baudrate=self.baudrate)
        except Exception as e:
            # print(e)
            error = "uart connect error"
            self.write_error_log(msg=error)
            self.uart_handle = None
            return False, error
        return True, None

    def handshake_device(self, device: str):
        """
        * device, model:
            * sa, N9020B
            * vsg, N5172B or E4438C
            * cwm, CMW270
            * dcp, E36312A
        """
        model = device_model[device]
        result = dict()
        if device == "sa":
            try:
                if self.sa_api:
                    self.sa_api.close_client()
                self.sa_api = SaFunc(ip=self.sa_ip, port=self.socket_port)
                host_id = self.sa_api.device_identification_query()
                temp = re.split(r",", host_id)
                cur_device_name = temp[1].strip()
                info = "device:{}--model:{}--cur_device_model {}".format(device, model, cur_device_name)
                if model == cur_device_name:
                    result[device] = True
                else:
                    self.write_error_log(msg=info)
                    result[device] = False
            except socket.timeout:
                error = "connect {} timeout".format(device)
                self.write_error_log(msg=error)
                result[device] = False
            except socket.error as e:
                error = "connect {} error".format(device)
                self.write_error_log(msg=error)
                result[device] = False
        if device == "vsg":
            try:
                if self.vsg_api:
                    self.vsg_api.close_client()
                self.vsg_api = VsgFunc(ip=self.vsg_ip, port=self.socket_port)
                host_id = self.vsg_api.device_identification_query()
                temp = re.split(r",", host_id)
                cur_device_name = temp[1].strip()
                info = "device:{}--model:{}--cur_device_model {}".format(device, model, cur_device_name)
                if model == cur_device_name:
                    result[device] = True
                else:
                    self.write_error_log(msg=info)
                    result[device] = False
            except socket.timeout:
                error = "connect {} timeout".format(device)
                self.write_error_log(msg=error)
                result[device] = False
            except socket.error as e:
                error = "connect {} error".format(device)
                self.write_error_log(msg=error)
                result[device] = False
        if device == "cmw":
            try:
                if self.cmw_api:
                    self.cmw_api.close_client()
                self.cmw_api = CmwFunc(ip=self.cmw_ip, port=self.socket_port)
                host_id = self.cmw_api.device_identification_query()
                temp = re.split(r",", host_id)
                cur_device_name = temp[1].strip()
                info = "device:{}--model:{}--cur_device_model {}".format(device, model, cur_device_name)
                if model == cur_device_name:
                    result[device] = True
                else:
                    self.write_error_log(msg=info)
                    result[device] = False
            except socket.timeout:
                error = "connect {} timeout".format(device)
                self.write_error_log(msg=error)
                result[device] = False
            except socket.error as e:
                error = "connect {} error".format(device)
                self.write_error_log(msg=error)
                result[device] = False
        if device == "dcp":
            try:
                if self.dcp_api:
                    self.dcp_api.close_client()
                self.dcp_api = DcPowerFunc(ip=self.dcp_ip, port=self.socket_port)
                host_id = self.dcp_api.device_identification_query()
                temp = re.split(r",", host_id)
                cur_device_name = temp[1].strip()
                info = "device:{}--model:{}--cur_device_model {}".format(device, model, cur_device_name)
                if model == cur_device_name:
                    result[device] = True
                else:
                    self.write_error_log(msg=info)
                    result[device] = False
            except socket.timeout:
                error = "connect {} timeout".format(device)
                self.write_error_log(msg=error)
                result[device] = False
            except socket.error as e:
                error = "connect {} error".format(device)
                self.write_error_log(msg=error)
                result[device] = False
        return result

    def init_device_api(self):
        if self.select_sa:
            ret, error = self.init_diff_device_api(device="spectrum_analyzer")
            if ret is False:
                return ret, error
        if self.select_vsg:
            ret, error = self.init_diff_device_api(device="signal_generator")
            if ret is False:
                return ret, error
        if self.select_cmw:
            ret, error = self.init_diff_device_api(device="bluetooth_tester")
            if ret is False:
                return ret, error
        if self.select_dcp:
            ret, error = self.init_diff_device_api(device="dc_power")
            if ret is False:
                return ret, error
        return True, None

    def init_diff_device_api(self, device: str):
        """
        * device:
            * sa:      N9010B
            * vsg:      N5172B
            * cwm:      CMW270
            * dcp:      E36312A
            * sfubtc:   SFU or BTC
        """
        try:
            if device == "spectrum_analyzer":
                if self.sa_api:
                    self.sa_api.close_client()
                self.sa_api = SaFunc(ip=self.sa_ip, port=self.socket_port)
                self.device_api[device] = self.sa_api
                return True, None
            elif device == "signal_generator":
                if self.vsg_api:
                    self.vsg_api.close_client()
                self.vsg_api = VsgFunc(ip=self.vsg_ip, port=self.socket_port)
                self.device_api[device] = self.vsg_api
                return True, None
            elif device == "bluetooth_tester":
                if self.cmw_api:
                    self.cmw_api.close_client()
                self.cmw_api = CmwFunc(ip=self.cmw_ip, port=self.socket_port)
                self.device_api[device] = self.cmw_api
                return True, None
            elif device == "dc_power":
                if self.dcp_api:
                    self.dcp_api.close_client()
                self.dcp_api = DcPowerFunc(ip=self.dcp_ip, port=self.socket_port)
                self.device_api[device] = self.dcp_api
                return True, None
            else:
                error = "select device error"
                self.write_error_log(msg=error)
                return False, error
        except socket.timeout:
            error = "connect {} timeout".format(device)
            self.write_error_log(msg=error)
            self.device_api[device] = None
            return False, error
        except socket.error as e:
            error = "connect {} error".format(device)
            self.write_error_log(msg=error)
            self.device_api[device] = None
            return False, error

    def check_diff_device_connection_correct(self, device: str, device_name: str or list):
        """
        * device, device_name:
            * sa, N9020B
            * vsg, N5172B or E4438C
            * cwm, CMW270
            * dcp, E36312A
            * sfubtc, ["R&S BTC", "SFU"]
        """
        # self.write_info_log("{}".format(self.device_api))
        host_id = self.device_api[device].device_identification_query()
        info = "{} host_id: {}".format(device, host_id)
        temp = re.split(r",", host_id)
        cur_device_name = temp[1].strip()
        if device == "sfubtc":
            if cur_device_name in device_name:
                self.write_info_log(msg=info)
                return True, None
            else:
                error = "获取{}设备型号与实际不符".format(device_name)
                return False, error
        else:
            if cur_device_name == device_name:
                self.write_info_log(msg=info)
                return True, None
            else:
                error = "获取{}设备型号与实际不符".format(device_name)
                return False, error

    def check_device_connection_correct(self):
        if self.select_sa:
            if self.sa_api is None:
                return False
            ret, error = self.check_diff_device_connection_correct(
                device="spectrum_analyzer", device_name=device_model["spectrum_analyzer"])
            if ret is False:
                self.sa_api.close_client()
                return ret, error
        if self.select_vsg:
            if self.vsg_api is None:
                return False
            ret, error = self.check_diff_device_connection_correct(
                device="signal_generator", device_name=device_model["signal_generator"])
            if ret is False:
                self.vsg_api.close_client()
                return ret, error
        if self.select_cmw:
            if self.cmw_api is None:
                return False
            ret, error = self.check_diff_device_connection_correct(
                device="bluetooth_tester", device_name=device_model["bluetooth_tester"])
            if ret is False:
                self.cmw_api.close_client()
                return ret, error
        if self.select_dcp:
            if self.dcp_api is None:
                return False
            ret, error = self.check_diff_device_connection_correct(
                device="dc_power", device_name=device_model["dc_power"])
            if ret is False:
                self.dcp_api.close_client()
                return ret, error
        return True, None

    def disconnection_device(self):
        if self.select_sa:
            if self.sa_api:
                self.sa_api.close_client()
        if self.select_vsg:
            if self.vsg_api:
                self.vsg_api.close_client()
        if self.select_cmw:
            if self.cmw_api:
                self.cmw_api.close_client()
        if self.select_dcp:
            if self.dcp_api:
                self.dcp_api.close_client()
        if self.uart_handle:
            self.uart_handle.close()

    def get_cur_tc_name(self, cur_tc_file: str):
        """
        cur_tc_file: "xxx/xxx/xxx/gxbtt/testcases/通用测试用例/RF发射测试/tc_00_case_sa_vsg.py"
        "test_case": {
            "RF发射测试": [
                {"name":"RF发射测试1", "file":"./test/test1.py"},
                {"name":"RF发射测试2", "file":"./test/test2.py"},
                {"name":"RF发射测试3", "file":"./test/test3.py"}
                ],
            "RF接收测试": [
                {"name":"RF接收测试1", "file":"./test/test1.py"},
                {"name":"RF接收测试2", "file":"./test/test2.py"},
                {"name":"RF接收测试3", "file":"./test/test3.py"}
                ],
            }
        """
        all_select_tc = self.data["test_case"]
        for case_set_name in all_select_tc.keys():
            cur_case_set = all_select_tc[case_set_name]
            for case_info in cur_case_set:
                case_name = case_info["name"]
                case_file = case_info["file"]
                if case_file == cur_tc_file:
                    return True, case_set_name, case_name
        return False, None, None

    def init_cur_testcase_report_file(self, cur_tc_file: str):
        report_path_info = dict()
        save_report_path = self.data["save_report_path"]
        ret, cur_case_set, case_name = self.get_cur_tc_name(cur_tc_file=cur_tc_file)
        if ret:
            timestamp = (re.sub("[-:]", '', str(datetime.now())[:19])).replace(" ", "_")
            tc_set_report_path = os.path.join(save_report_path, "{}用例集_测试报告".format(cur_case_set))
            tc_report_dir_name = "{}_report_{}".format(case_name, timestamp)
            tc_report_name = "{}.xlsx".format(tc_report_dir_name)
            tc_report_dir_path = os.path.join(tc_set_report_path, tc_report_dir_name)
            tc_report_path = os.path.join(tc_report_dir_path, tc_report_name)
            self.write_info_log(tc_set_report_path)
            self.write_info_log(tc_report_dir_path)
            self.write_info_log(tc_report_path)
            report_path_info["case_name"] = case_name
            report_path_info["tc_set_report_path"] = tc_set_report_path
            report_path_info["tc_report_dir_path"] = tc_report_dir_path
            report_path_info["tc_report_path"] = tc_report_path
            if not os.path.exists(tc_report_dir_path):
                os.makedirs(tc_report_dir_path)
            return True, None, report_path_info
        else:
            error = "当前用例不在self.data中"
            return False, error, None

    def print_something(self):
        # print("print in device_run_func_class 1")
        print("print in device_run_func_class 2")

    def print_other_something(self):
        # print("print in device_run_func_class 3")
        print("print in device_run_func_class 4")
