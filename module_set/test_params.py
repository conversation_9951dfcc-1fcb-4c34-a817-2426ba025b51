# -*- coding: utf-8 -*-
# **** begin **** Sagitta 测试必要数据 ****

sagitta_signal_channel_chg = {
    1: [i for i in range(0, 0 + 1)],
    2: [(2 + 2 * i) for i in range(0, 9 + 1)] + [(2 + 2 * i) for i in range(11, 37 + 1)],
    4: [(3 + 4 * i) for i in range(0, 4 + 1)] + [(3 + 4 * i) for i in range(6, 24 + 1)]
}

sagitta_symbol_rate_chg = {0: 1, 1: 2, 2: 4}

sagitta_scode_rate_chg = {2: 2/8, 3: 3/8, 4: 4/8, 5: 5/8, 6: 6/8, 7: 7/8}

sagitta_scode_mode_chg = {0: 'bypass', 1: 'scode_en_with_crc8', 2: 'scode_en_without_crc8'}

arb_file_str_scode_rate = {
    2/8: '2_8',
    3/8: '3_8',
    4/8: '4_8',
    5/8: '5_8',
    6/8: '6_8',
    7/8: '7_8'
}

arb_file_fps = {
    1: 1000,
    2: 2000,
    4: 4000
}

arb_file_ptype = {
    2/8: 0,
    3/8: 1,
    4/8: 2,
    5/8: 3,
    6/8: 4,
    7/8: 5
}

arb_file_bk = {
    2/8: 10,
    3/8: 5,
    4/8: 4,
    5/8: 3,
    6/8: 2,
    7/8: 2
}

arb_file_snr_chg = {
    1: 0,
    2: 1,
    3: 2,
    4: 3,
    5: 4,
    6: 5,
}

sagitta_report_title = {
    'Gauss': [
        'Symbol Rate', 'Signal Channel', 'S-Code_en',
        'S-Code Rate', 'SNR', 'Data Length',
        'Test Frequency (MHz)', 'Rd_data1', 'Rd_data2', 'PER (%)', 'Polar Error'
    ],
    'Sensitivity': [
        'Symbol Rate', 'Signal Channel', 'S-Code Enable',
        'S-Code Rate', 'Data Length', 'Test Frequency (MHz)',
        'Input Power (dBm)', 'PER (%)', 'Sensitivity (dBm)'
    ]
}

# **** end **** Sagitta 测试必要数据 ****