# -*- coding: utf-8 -*-
"""
测试sagitta报告函数的脚本
"""

import os
import sys
from module_set.handle_report_class import HandleExcelReport

def test_sagitta_report_functions():
    """测试sagitta报告相关函数"""
    
    # 创建测试报告文件
    test_report_path = "./test_sagitta_report.xlsx"
    
    # 如果文件存在则删除，确保测试从空文件开始
    if os.path.exists(test_report_path):
        os.remove(test_report_path)
    
    handle = HandleExcelReport(filename=test_report_path)
    
    print("=== 测试sagitta报告函数 ===")
    
    # 测试1: 检查空sheet
    print("\n1. 测试空sheet检查功能")
    is_empty = handle.is_sheet_empty("test_sheet")
    print("新创建的sheet是否为空: {}".format(is_empty))
    
    # 测试2: 获取不同测试类型的表头
    print("\n2. 测试表头获取功能")
    gauss_headers = handle.get_sagitta_test_headers("Gauss")
    print("Gauss测试表头: {}".format(gauss_headers))
    
    sensitivity_headers = handle.get_sagitta_test_headers("Sensitivity")
    print("Sensitivity测试表头: {}".format(sensitivity_headers))
    
    default_headers = handle.get_sagitta_test_headers("Unknown")
    print("未知测试类型表头: {}".format(default_headers))
    
    # 测试3: 写入Gauss测试数据（第一次写入，应该自动添加表头）
    print("\n3. 测试Gauss数据写入（自动添加表头）")
    gauss_data_1 = [0, 10, 1, 4, 2, 6, 2412.007, 0.05, '1.2e-03~3.4e-03']
    handle.write_sagitta_test_data_with_auto_header('6byte_Gauss', 'Gauss', gauss_data_1)
    print("第一行Gauss数据已写入")
    
    # 测试4: 再次写入Gauss测试数据（不应该重复添加表头）
    print("\n4. 测试Gauss数据写入（不重复表头）")
    gauss_data_2 = [1, 15, 1, 5, 3, 6, 2417.007, 0.08, '2.1e-03~4.2e-03']
    handle.write_sagitta_test_data_with_auto_header('6byte_Gauss', 'Gauss', gauss_data_2)
    print("第二行Gauss数据已写入")
    
    # 测试5: 写入Sensitivity测试数据
    print("\n5. 测试Sensitivity数据写入")
    sens_data_1 = [0, 10, 1, 4, 6, 2412.007, -85, 0.1, -84.5]
    handle.write_sagitta_test_data_with_auto_header('6byte_Sensitivity', 'Sensitivity', sens_data_1)
    print("第一行Sensitivity数据已写入")
    
    sens_data_2 = [1, 15, 1, 5, 6, 2417.007, -80, 0.15, -79.2]
    handle.write_sagitta_test_data_with_auto_header('6byte_Sensitivity', 'Sensitivity', sens_data_2)
    print("第二行Sensitivity数据已写入")
    
    # 测试6: 检查sheet是否还为空
    print("\n6. 测试写入数据后的空检查")
    is_gauss_empty = handle.is_sheet_empty('6byte_Gauss')
    is_sens_empty = handle.is_sheet_empty('6byte_Sensitivity')
    print("Gauss sheet是否为空: {}".format(is_gauss_empty))
    print("Sensitivity sheet是否为空: {}".format(is_sens_empty))
    
    print("\n=== 测试完成 ===")
    print("测试报告文件已生成: {}".format(test_report_path))
    print("请打开Excel文件查看结果")

if __name__ == "__main__":
    test_sagitta_report_functions()
