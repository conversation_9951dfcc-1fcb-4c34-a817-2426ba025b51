2025-06-05 09:15:56,851  ERROR: signal_generator设备型号不匹配：预期设备型号：E4438C--实际设备型号：CMW
2025-06-05 09:15:59,812  ERROR: signal_generator设备型号不匹配：预期设备型号：E4438C--实际设备型号：CMW
2025-06-05 09:16:01,726  ERROR: signal_generator设备型号不匹配：预期设备型号：E4438C--实际设备型号：CMW
2025-06-05 09:18:32,392  INFO: 测试开始!
2025-06-05 09:18:32,394  INFO: 测试进度：0/1
2025-06-05 09:18:32,394  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-05 09:18:32,394  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-05 09:18:32,428  INFO: === 初始化测试环境 ===
2025-06-05 09:18:32,429  INFO: 正在初始化设备连接...
2025-06-05 09:18:32,504  INFO: bluetooth_tester host_id: <PERSON><PERSON><PERSON>&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-05 09:18:32,518  INFO: 正在创建测试报告...
2025-06-05 09:18:32,518  INFO: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告
2025-06-05 09:18:32,520  INFO: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250605_091832
2025-06-05 09:18:32,523  INFO: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250605_091832/superlink_rx_gauss_report_20250605_091832.xlsx
2025-06-05 09:18:32,525  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250605_091832/superlink_rx_gauss_report_20250605_091832.xlsx
2025-06-05 09:18:32,526  INFO: === 开始高斯测试 ===
2025-06-05 09:18:32,528  INFO: === 生成高斯测试用例 ===
2025-06-05 09:18:32,529  INFO: 高斯测试参数配置:
2025-06-05 09:18:32,529  INFO:   SYM_RATES: [0]
2025-06-05 09:18:32,529  INFO:   S_CODE_EN: [1]
2025-06-05 09:18:32,529  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-05 09:18:32,530  INFO:   SNRS: [1, 2, 3, 4, 5, 6]
2025-06-05 09:18:32,530  INFO:   DATA_LEN: [6]
2025-06-05 09:18:32,530  INFO: 测试用例数量(36)超过限制(2)，将只执行前2个用例
2025-06-05 09:18:32,530  INFO: 成功生成 2 个高斯测试用例
2025-06-05 09:18:32,531  INFO: 总测试用例数: 2
2025-06-05 09:18:32,531  INFO: 
=== 执行测试用例 1/2 ===
2025-06-05 09:18:32,531  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 1, 'signal_ch': 0}
2025-06-05 09:18:32,531  INFO: 开始配置CMW信号发生器...
2025-06-05 09:18:33,538  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-05 09:18:33,539  INFO: 开始加载ARB文件...
2025-06-05 09:18:34,555  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-05 09:20:25,078  INFO: 初始化RF板...
2025-06-05 09:20:25,396  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-05 09:21:19,675  INFO: 测试成功 - PER: 32.141471843153845%, 极性误差: 8.4229e-03~5.9082e-02
2025-06-05 09:21:19,713  INFO: 
=== 执行测试用例 2/2 ===
2025-06-05 09:21:19,714  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 2, 'signal_ch': 0}
2025-06-05 09:21:19,715  INFO: 开始配置CMW信号发生器...
2025-06-05 09:21:25,743  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-05 09:21:25,744  INFO: 开始加载ARB文件...
2025-06-05 09:21:25,969  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym1db_grdc.wv"
2025-06-05 09:23:11,477  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-05 09:23:45,010  INFO: 测试成功 - PER: 12.601908614042845%, 极性误差: 1.8311e-03~1.2329e-02
2025-06-05 09:23:45,041  INFO: === 高斯测试完成 ===
2025-06-05 09:23:45,042  INFO: === 清理测试环境 ===
2025-06-05 09:23:45,046  INFO: 设备连接已断开
2025-06-05 09:23:45,046  INFO: === 测试完成 ===
2025-06-05 09:23:45,046  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-06-05 09:23:45,047  INFO: 测试进度：1/1
2025-06-05 09:23:50,053  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-06-05 09:23:50,056  INFO: 测试完成！总共测试耗时：00:05:17
2025-06-05 09:27:50,589  INFO: 测试开始!
2025-06-05 09:27:50,595  INFO: 测试进度：0/1
2025-06-05 09:27:50,596  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-05 09:27:50,596  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-05 09:27:50,622  INFO: === 初始化测试环境 ===
2025-06-05 09:27:50,623  INFO: 正在初始化设备连接...
2025-06-05 09:27:50,706  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-05 09:27:50,719  INFO: 正在创建测试报告...
2025-06-05 09:27:50,719  INFO: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告
2025-06-05 09:27:50,719  INFO: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250605_092750
2025-06-05 09:27:50,719  INFO: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250605_092750/superlink_rx_gauss_report_20250605_092750.xlsx
2025-06-05 09:27:50,720  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250605_092750/superlink_rx_gauss_report_20250605_092750.xlsx
2025-06-05 09:27:50,721  INFO: === 开始高斯测试 ===
2025-06-05 09:27:50,721  INFO: === 生成高斯测试用例 ===
2025-06-05 09:27:50,722  INFO: 高斯测试参数配置:
2025-06-05 09:27:50,722  INFO:   SYM_RATES: [0]
2025-06-05 09:27:50,722  INFO:   S_CODE_EN: [1]
2025-06-05 09:27:50,723  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-05 09:27:50,732  INFO:   SNRS: [1, 2, 3, 4, 5, 6]
2025-06-05 09:27:50,733  INFO:   DATA_LEN: [6]
2025-06-05 09:27:50,733  INFO: 测试用例数量(36)超过限制(2)，将只执行前2个用例
2025-06-05 09:27:50,734  INFO: 成功生成 2 个高斯测试用例
2025-06-05 09:27:50,734  INFO: 总测试用例数: 2
2025-06-05 09:27:50,734  INFO: 
=== 执行测试用例 1/2 ===
2025-06-05 09:27:50,735  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 1, 'signal_ch': 0}
2025-06-05 09:27:50,735  INFO: 开始配置CMW信号发生器...
2025-06-05 09:27:56,742  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-05 09:27:56,743  INFO: 开始加载ARB文件...
2025-06-05 09:27:56,964  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-05 09:29:07,361  INFO: 初始化RF板...
2025-06-05 09:29:07,684  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-05 09:31:45,096  INFO: 测试成功 - PER: 30.903954802259893%, 极性误差: 1.1108e-02~2.1484e-02
2025-06-05 09:31:45,130  INFO: 
=== 执行测试用例 2/2 ===
2025-06-05 09:31:45,130  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 2, 'signal_ch': 0}
2025-06-05 09:31:45,132  INFO: 开始配置CMW信号发生器...
2025-06-05 09:31:51,155  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-05 09:31:51,156  INFO: 开始加载ARB文件...
2025-06-05 09:31:51,389  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym1db_grdc.wv"
2025-06-05 09:33:01,796  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-05 09:33:32,229  INFO: 测试成功 - PER: 12.657538509413413%, 极性误差: 3.2959e-03~1.6235e-02
2025-06-05 09:33:32,255  INFO: === 高斯测试完成 ===
2025-06-05 09:33:32,256  INFO: === 清理测试环境 ===
2025-06-05 09:33:32,258  INFO: 设备连接已断开
2025-06-05 09:33:32,259  INFO: === 测试完成 ===
2025-06-05 09:33:32,260  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-06-05 09:33:32,262  INFO: 测试进度：1/1
2025-06-05 09:33:37,259  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-06-05 09:33:37,263  INFO: 测试完成！总共测试耗时：00:03:54
2025-06-05 09:44:01,568  INFO: 测试开始!
2025-06-05 09:44:01,570  INFO: 测试进度：0/1
2025-06-05 09:44:01,571  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-05 09:44:01,571  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-05 09:44:01,604  INFO: === 初始化测试环境 ===
2025-06-05 09:44:01,604  INFO: 正在初始化设备连接...
2025-06-05 09:44:01,675  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-05 09:44:01,688  INFO: 正在创建测试报告...
2025-06-05 09:44:01,689  INFO: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告
2025-06-05 09:44:01,689  INFO: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250605_094401
2025-06-05 09:44:01,690  INFO: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250605_094401/superlink_rx_gauss_report_20250605_094401.xlsx
2025-06-05 09:44:01,690  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250605_094401/superlink_rx_gauss_report_20250605_094401.xlsx
2025-06-05 09:44:01,690  INFO: === 开始高斯测试 ===
2025-06-05 09:44:01,691  INFO: === 生成高斯测试用例 ===
2025-06-05 09:44:01,691  INFO: 高斯测试参数配置:
2025-06-05 09:44:01,691  INFO:   SYM_RATES: [0]
2025-06-05 09:44:01,692  INFO:   S_CODE_EN: [1]
2025-06-05 09:44:01,694  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-05 09:44:01,701  INFO:   SNRS: [1, 2, 3, 4, 5, 6]
2025-06-05 09:44:01,702  INFO:   DATA_LEN: [6]
2025-06-05 09:44:01,703  INFO: 测试用例数量(36)超过限制(2)，将只执行前2个用例
2025-06-05 09:44:01,704  INFO: 成功生成 2 个高斯测试用例
2025-06-05 09:44:01,704  INFO: 总测试用例数: 2
2025-06-05 09:44:01,705  INFO: 
=== 执行测试用例 1/2 ===
2025-06-05 09:44:01,705  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 1, 'signal_ch': 0}
2025-06-05 09:44:01,705  INFO: 开始配置CMW信号发生器...
2025-06-05 09:44:07,714  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-05 09:44:07,717  INFO: 开始加载ARB文件...
2025-06-05 09:44:08,064  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-05 09:45:18,455  INFO: 初始化RF板...
2025-06-05 09:45:18,774  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-05 09:46:01,527  INFO: 测试成功 - PER: 32.16414488766621%, 极性误差: 9.2773e-03~6.8481e-02
2025-06-05 09:46:01,570  INFO: 
=== 执行测试用例 2/2 ===
2025-06-05 09:46:01,570  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 2, 'signal_ch': 0}
2025-06-05 09:46:01,571  INFO: 开始配置CMW信号发生器...
2025-06-05 09:46:07,639  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-05 09:46:07,640  INFO: 开始加载ARB文件...
2025-06-05 09:46:07,872  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym1db_grdc.wv"
2025-06-05 09:47:18,278  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-05 09:47:49,061  INFO: 测试成功 - PER: 12.581882089790696%, 极性误差: 1.5869e-03~7.8125e-03
2025-06-05 09:47:49,107  INFO: === 高斯测试完成 ===
2025-06-05 09:47:49,107  INFO: === 清理测试环境 ===
2025-06-05 09:47:49,114  INFO: 设备连接已断开
2025-06-05 09:47:49,115  INFO: === 测试完成 ===
2025-06-05 09:47:49,115  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-06-05 09:47:49,115  INFO: 测试进度：1/1
2025-06-05 09:47:54,117  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-06-05 09:47:54,122  INFO: 测试完成！总共测试耗时：00:03:52
2025-06-05 09:51:56,809  INFO: 测试开始!
2025-06-05 09:51:56,810  INFO: 测试进度：0/1
2025-06-05 09:51:56,810  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-05 09:51:56,811  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-05 09:51:56,827  INFO: === 初始化测试环境 ===
2025-06-05 09:51:56,828  INFO: 正在初始化设备连接...
2025-06-05 09:51:56,893  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-05 09:51:56,903  INFO: 正在创建测试报告...
2025-06-05 09:51:56,904  INFO: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告
2025-06-05 09:51:56,907  INFO: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250605_095156
2025-06-05 09:51:56,908  INFO: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250605_095156/superlink_rx_gauss_report_20250605_095156.xlsx
2025-06-05 09:51:56,909  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250605_095156/superlink_rx_gauss_report_20250605_095156.xlsx
2025-06-05 09:51:56,910  INFO: === 开始高斯测试 ===
2025-06-05 09:51:56,910  INFO: === 生成高斯测试用例 ===
2025-06-05 09:51:56,913  INFO: 高斯测试参数配置:
2025-06-05 09:51:56,913  INFO:   SYM_RATES: [0]
2025-06-05 09:51:56,914  INFO:   S_CODE_EN: [1]
2025-06-05 09:51:56,914  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-05 09:51:56,914  INFO:   SNRS: [1, 2, 3, 4, 5, 6]
2025-06-05 09:51:56,914  INFO:   DATA_LEN: [6]
2025-06-05 09:51:56,914  INFO: 测试用例数量(36)超过限制(10)，将只执行前10个用例
2025-06-05 09:51:56,915  INFO: 成功生成 10 个高斯测试用例
2025-06-05 09:51:56,915  INFO: 总测试用例数: 10
2025-06-05 09:51:56,915  INFO: 
=== 执行测试用例 1/10 ===
2025-06-05 09:51:56,915  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 1, 'signal_ch': 0}
2025-06-05 09:51:56,916  INFO: 开始配置CMW信号发生器...
2025-06-05 09:52:02,939  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-05 09:52:02,939  INFO: 开始加载ARB文件...
2025-06-05 09:52:03,161  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-05 09:53:18,019  INFO: 初始化RF板...
2025-06-05 09:53:21,376  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-05 09:58:05,460  ERROR: 测试失败: min() arg is an empty sequence
2025-06-05 09:58:05,783  INFO: 
=== 执行测试用例 2/10 ===
2025-06-05 09:58:05,783  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 2, 'signal_ch': 0}
2025-06-05 09:58:05,784  INFO: 开始配置CMW信号发生器...
2025-06-05 09:59:06,055  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-05 09:59:06,055  INFO: 开始加载ARB文件...
2025-06-05 09:59:36,090  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym1db_grdc.wv"
2025-06-05 10:00:47,152  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-05 10:04:25,039  ERROR: 测试失败: min() arg is an empty sequence
2025-06-05 10:04:25,096  INFO: 
=== 执行测试用例 3/10 ===
2025-06-05 10:04:25,096  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 3, 'signal_ch': 0}
2025-06-05 10:04:25,097  INFO: 开始配置CMW信号发生器...
2025-06-05 10:04:55,270  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-05 10:04:55,274  INFO: 开始加载ARB文件...
2025-06-05 10:04:58,615  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym2db_grdc.wv"
2025-06-05 10:06:20,941  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-05 10:08:19,903  ERROR: 测试失败: min() arg is an empty sequence
2025-06-05 10:08:19,977  INFO: 
=== 执行测试用例 4/10 ===
2025-06-05 10:08:19,977  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 4, 'signal_ch': 0}
2025-06-05 10:08:19,978  INFO: 开始配置CMW信号发生器...
2025-06-05 10:08:39,452  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-05 10:08:39,540  INFO: 开始加载ARB文件...
2025-06-05 10:08:50,132  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym3db_grdc.wv"
2025-06-05 10:10:15,851  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-05 10:13:24,314  ERROR: 测试失败: min() arg is an empty sequence
2025-06-05 10:13:24,371  INFO: 
=== 执行测试用例 5/10 ===
2025-06-05 10:13:24,371  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 5, 'signal_ch': 0}
2025-06-05 10:13:24,373  INFO: 开始配置CMW信号发生器...
2025-06-05 10:14:04,286  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-05 10:14:04,286  INFO: 开始加载ARB文件...
2025-06-05 10:14:17,164  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym4db_grdc.wv"
2025-06-05 10:15:27,111  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-05 10:17:18,404  ERROR: 测试失败: min() arg is an empty sequence
2025-06-05 10:17:18,662  INFO: 
=== 执行测试用例 6/10 ===
2025-06-05 10:17:18,663  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 6, 'signal_ch': 0}
2025-06-05 10:17:18,665  INFO: 开始配置CMW信号发生器...
2025-06-05 10:17:37,435  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-05 10:17:37,443  INFO: 开始加载ARB文件...
2025-06-05 10:17:43,464  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym5db_grdc.wv"
2025-06-05 10:19:08,749  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-05 10:20:53,352  ERROR: 测试失败: min() arg is an empty sequence
2025-06-05 10:20:53,486  INFO: 
=== 执行测试用例 7/10 ===
2025-06-05 10:20:53,487  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 3, 'snr': 1, 'signal_ch': 0}
2025-06-05 10:20:53,488  INFO: 开始配置CMW信号发生器...
2025-06-05 10:21:11,716  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-05 10:21:11,716  INFO: 开始加载ARB文件...
2025-06-05 10:21:13,356  ERROR: ARB文件不存在: ARB 文件不存在
2025-06-05 10:21:13,449  INFO: 
=== 执行测试用例 8/10 ===
2025-06-05 10:21:13,452  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 3, 'snr': 2, 'signal_ch': 0}
2025-06-05 10:21:13,458  INFO: 开始配置CMW信号发生器...
2025-06-05 10:21:25,096  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-05 10:21:25,098  INFO: 开始加载ARB文件...
2025-06-05 10:21:37,408  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar3_8\qpsk_1Rs_24Fs_1000fps_PType1_seed0_5bk_sym1db_grdc.wv"
2025-06-05 10:23:12,914  INFO: 发送测试命令: bb test_cmw 0 0 1 3 6
2025-06-05 10:26:41,600  ERROR: 测试失败: min() arg is an empty sequence
2025-06-05 10:26:41,659  INFO: 
=== 执行测试用例 9/10 ===
2025-06-05 10:26:41,659  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 3, 'snr': 3, 'signal_ch': 0}
2025-06-05 10:26:41,662  INFO: 开始配置CMW信号发生器...
2025-06-05 10:27:52,172  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-05 10:27:52,174  INFO: 开始加载ARB文件...
2025-06-05 10:28:22,205  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar3_8\qpsk_1Rs_24Fs_1000fps_PType1_seed0_5bk_sym2db_grdc.wv"
2025-06-05 10:29:54,673  INFO: 发送测试命令: bb test_cmw 0 0 1 3 6
2025-06-05 10:31:22,022  INFO: 测试成功 - PER: 4.816074188562592%, 极性误差: 4.2725e-03~4.2725e-03
2025-06-05 10:31:22,080  INFO: 
=== 执行测试用例 10/10 ===
2025-06-05 10:31:22,081  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 3, 'snr': 4, 'signal_ch': 0}
2025-06-05 10:31:22,083  INFO: 开始配置CMW信号发生器...
2025-06-05 10:32:03,564  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-05 10:32:03,567  INFO: 开始加载ARB文件...
2025-06-05 10:32:17,381  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar3_8\qpsk_1Rs_24Fs_1000fps_PType1_seed0_5bk_sym3db_grdc.wv"
2025-06-05 10:33:31,164  INFO: 发送测试命令: bb test_cmw 0 0 1 3 6
2025-06-05 10:34:04,369  INFO: 测试成功 - PER: 0.9844069932272825%, 极性误差: 3.6621e-04~3.4180e-03
2025-06-05 10:34:04,411  INFO: === 高斯测试完成 ===
2025-06-05 10:34:04,412  INFO: === 清理测试环境 ===
2025-06-05 10:34:04,414  INFO: 设备连接已断开
2025-06-05 10:34:04,414  INFO: === 测试完成 ===
2025-06-05 10:34:04,414  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-06-05 10:34:04,415  INFO: 测试进度：1/1
2025-06-05 10:34:09,420  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-06-05 10:34:09,423  INFO: 测试完成！总共测试耗时：00:10:15
2025-06-05 10:37:04,776  ERROR: 请选择并勾选要测试的用例！
2025-06-05 10:37:08,590  INFO: 测试开始!
2025-06-05 10:37:08,591  INFO: 测试进度：0/1
2025-06-05 10:37:08,592  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-05 10:37:08,593  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-05 10:37:08,614  INFO: === 初始化测试环境 ===
2025-06-05 10:37:08,614  INFO: 正在初始化设备连接...
2025-06-05 10:37:08,678  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-05 10:37:08,690  INFO: 正在创建测试报告...
2025-06-05 10:37:08,690  INFO: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告
2025-06-05 10:37:08,692  INFO: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250605_103708
2025-06-05 10:37:08,692  INFO: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250605_103708/superlink_rx_gauss_report_20250605_103708.xlsx
2025-06-05 10:37:08,693  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250605_103708/superlink_rx_gauss_report_20250605_103708.xlsx
2025-06-05 10:37:08,693  INFO: === 开始高斯测试 ===
2025-06-05 10:37:08,693  INFO: === 生成高斯测试用例 ===
2025-06-05 10:37:08,694  INFO: 高斯测试参数配置:
2025-06-05 10:37:08,694  INFO:   SYM_RATES: [0]
2025-06-05 10:37:08,695  INFO:   S_CODE_EN: [1]
2025-06-05 10:37:08,699  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-05 10:37:08,700  INFO:   SNRS: [1, 2, 3, 4, 5, 6]
2025-06-05 10:37:08,700  INFO:   DATA_LEN: [6]
2025-06-05 10:37:08,700  INFO: 测试用例数量(36)超过限制(10)，将只执行前10个用例
2025-06-05 10:37:08,700  INFO: 成功生成 10 个高斯测试用例
2025-06-05 10:37:08,701  INFO: 总测试用例数: 10
2025-06-05 10:37:08,701  INFO: 
=== 执行测试用例 1/10 ===
2025-06-05 10:37:08,701  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 1, 'signal_ch': 0}
2025-06-05 10:37:08,702  INFO: 开始配置CMW信号发生器...
2025-06-05 10:37:14,720  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-05 10:37:14,726  INFO: 开始加载ARB文件...
2025-06-05 10:37:14,962  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-05 10:38:36,245  INFO: 初始化RF板...
2025-06-05 10:38:41,577  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-05 10:40:31,733  INFO: 测试成功 - PER: 31.2723592543777%, 极性误差: 1.4404e-02~1.4404e-02
2025-06-05 10:40:31,768  INFO: 
=== 执行测试用例 2/10 ===
2025-06-05 10:40:31,769  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 2, 'signal_ch': 0}
2025-06-05 10:40:31,771  INFO: 开始配置CMW信号发生器...
2025-06-05 10:40:37,834  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-05 10:40:37,835  INFO: 开始加载ARB文件...
2025-06-05 10:40:38,055  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym1db_grdc.wv"
2025-06-05 10:41:50,767  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-05 10:44:04,110  ERROR: 测试失败: min() arg is an empty sequence
2025-06-05 10:44:04,345  INFO: 
=== 执行测试用例 3/10 ===
2025-06-05 10:44:04,345  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 3, 'signal_ch': 0}
2025-06-05 10:44:04,347  INFO: 开始配置CMW信号发生器...
2025-06-05 10:44:34,147  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-05 10:44:34,147  INFO: 开始加载ARB文件...
2025-06-05 10:44:35,467  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym2db_grdc.wv"
2025-06-05 10:45:52,659  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-05 10:47:38,546  ERROR: 测试失败: min() arg is an empty sequence
2025-06-05 10:47:39,163  INFO: 
=== 执行测试用例 4/10 ===
2025-06-05 10:47:39,164  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 4, 'signal_ch': 0}
2025-06-05 10:47:39,164  INFO: 开始配置CMW信号发生器...
2025-06-05 10:47:51,502  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-05 10:47:51,504  INFO: 开始加载ARB文件...
2025-06-05 10:48:02,418  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym3db_grdc.wv"
2025-06-05 10:49:12,298  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-05 10:49:49,145  INFO: 测试停止!
2025-06-05 11:02:57,721  INFO: 测试开始!
2025-06-05 11:02:57,723  INFO: 测试进度：0/1
2025-06-05 11:02:57,724  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-05 11:02:57,724  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-05 11:02:57,766  INFO: === 初始化测试环境 ===
2025-06-05 11:02:57,767  INFO: 正在初始化设备连接...
2025-06-05 11:02:57,832  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-05 11:02:57,841  INFO: 正在创建测试报告...
2025-06-05 11:02:57,842  INFO: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告
2025-06-05 11:02:57,843  INFO: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250605_110257
2025-06-05 11:02:57,843  INFO: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250605_110257/superlink_rx_gauss_report_20250605_110257.xlsx
2025-06-05 11:02:57,844  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250605_110257/superlink_rx_gauss_report_20250605_110257.xlsx
2025-06-05 11:02:57,844  INFO: === 开始高斯测试 ===
2025-06-05 11:02:57,844  INFO: === 生成高斯测试用例 ===
2025-06-05 11:02:57,848  INFO: 高斯测试参数配置:
2025-06-05 11:02:57,849  INFO:   SYM_RATES: [0]
2025-06-05 11:02:57,849  INFO:   S_CODE_EN: [1]
2025-06-05 11:02:57,849  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-05 11:02:57,849  INFO:   SNRS: [1, 2, 3, 4, 5, 6]
2025-06-05 11:02:57,849  INFO:   DATA_LEN: [6]
2025-06-05 11:02:57,850  INFO: 测试用例数量(36)超过限制(10)，将只执行前10个用例
2025-06-05 11:02:57,850  INFO: 成功生成 10 个高斯测试用例
2025-06-05 11:02:57,850  INFO: 总测试用例数: 10
2025-06-05 11:02:57,850  INFO: 
=== 执行测试用例 1/10 ===
2025-06-05 11:02:57,850  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 1, 'signal_ch': 0}
2025-06-05 11:02:57,851  INFO: 开始配置CMW信号发生器...
2025-06-05 11:03:03,874  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-05 11:03:03,879  ERROR: 测试用例 1 执行失败: 'GaussTest' object has no attribute 'load_arb_file'
2025-06-05 11:03:03,923  INFO: 
=== 执行测试用例 2/10 ===
2025-06-05 11:03:03,924  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 2, 'signal_ch': 0}
2025-06-05 11:03:03,925  INFO: 开始配置CMW信号发生器...
2025-06-05 11:03:04,934  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-05 11:03:04,935  ERROR: 测试用例 2 执行失败: 'GaussTest' object has no attribute 'load_arb_file'
2025-06-05 11:03:04,976  INFO: 
=== 执行测试用例 3/10 ===
2025-06-05 11:03:04,976  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 3, 'signal_ch': 0}
2025-06-05 11:03:04,977  INFO: 开始配置CMW信号发生器...
2025-06-05 11:03:05,994  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-05 11:03:05,995  ERROR: 测试用例 3 执行失败: 'GaussTest' object has no attribute 'load_arb_file'
2025-06-05 11:03:06,039  INFO: 
=== 执行测试用例 4/10 ===
2025-06-05 11:03:06,039  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 4, 'signal_ch': 0}
2025-06-05 11:03:06,041  INFO: 开始配置CMW信号发生器...
2025-06-05 11:03:07,084  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-05 11:03:07,088  ERROR: 测试用例 4 执行失败: 'GaussTest' object has no attribute 'load_arb_file'
2025-06-05 11:03:07,356  INFO: 测试停止!
2025-06-05 11:04:09,147  INFO: 测试开始!
2025-06-05 11:04:09,148  INFO: 测试进度：0/1
2025-06-05 11:04:09,148  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-05 11:04:09,149  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-05 11:04:09,169  INFO: === 初始化测试环境 ===
2025-06-05 11:04:09,169  INFO: 正在初始化设备连接...
2025-06-05 11:04:09,215  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-05 11:04:09,224  INFO: 正在创建测试报告...
2025-06-05 11:04:09,224  INFO: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告
2025-06-05 11:04:09,225  INFO: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250605_110409
2025-06-05 11:04:09,226  INFO: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250605_110409/superlink_rx_gauss_report_20250605_110409.xlsx
2025-06-05 11:04:09,226  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250605_110409/superlink_rx_gauss_report_20250605_110409.xlsx
2025-06-05 11:04:09,227  INFO: === 开始高斯测试 ===
2025-06-05 11:04:09,228  INFO: === 生成高斯测试用例 ===
2025-06-05 11:04:09,228  INFO: 高斯测试参数配置:
2025-06-05 11:04:09,231  INFO:   SYM_RATES: [0]
2025-06-05 11:04:09,232  INFO:   S_CODE_EN: [1]
2025-06-05 11:04:09,233  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-05 11:04:09,233  INFO:   SNRS: [1, 2, 3, 4, 5, 6]
2025-06-05 11:04:09,233  INFO:   DATA_LEN: [6]
2025-06-05 11:04:09,233  INFO: 测试用例数量(36)超过限制(10)，将只执行前10个用例
2025-06-05 11:04:09,233  INFO: 成功生成 10 个高斯测试用例
2025-06-05 11:04:09,234  INFO: 总测试用例数: 10
2025-06-05 11:04:09,234  INFO: 
=== 执行测试用例 1/10 ===
2025-06-05 11:04:09,234  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 1, 'signal_ch': 0}
2025-06-05 11:04:09,234  INFO: 开始配置CMW信号发生器...
2025-06-05 11:04:10,252  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-05 11:04:10,253  ERROR: 测试用例 1 执行失败: 'GaussTest' object has no attribute 'load_arb_file'
2025-06-05 11:04:10,383  INFO: 
=== 执行测试用例 2/10 ===
2025-06-05 11:04:10,387  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 2, 'signal_ch': 0}
2025-06-05 11:04:10,388  INFO: 开始配置CMW信号发生器...
2025-06-05 11:04:11,392  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-05 11:04:11,393  ERROR: 测试用例 2 执行失败: 'GaussTest' object has no attribute 'load_arb_file'
2025-06-05 11:04:11,427  INFO: 
=== 执行测试用例 3/10 ===
2025-06-05 11:04:11,428  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 3, 'signal_ch': 0}
2025-06-05 11:04:11,430  INFO: 开始配置CMW信号发生器...
2025-06-05 11:04:12,159  INFO: 测试停止!
2025-06-05 11:05:16,974  INFO: 测试开始!
2025-06-05 11:05:16,980  INFO: 测试进度：0/1
2025-06-05 11:05:16,981  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-05 11:05:16,981  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-05 11:05:17,007  INFO: === 初始化测试环境 ===
2025-06-05 11:05:17,007  INFO: 正在初始化设备连接...
2025-06-05 11:05:17,056  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-05 11:05:17,067  INFO: 正在创建测试报告...
2025-06-05 11:05:17,067  INFO: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告
2025-06-05 11:05:17,068  INFO: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250605_110517
2025-06-05 11:05:17,069  INFO: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250605_110517/superlink_rx_gauss_report_20250605_110517.xlsx
2025-06-05 11:05:17,070  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250605_110517/superlink_rx_gauss_report_20250605_110517.xlsx
2025-06-05 11:05:17,071  INFO: === 开始高斯测试 ===
2025-06-05 11:05:17,073  INFO: === 生成高斯测试用例 ===
2025-06-05 11:05:17,073  INFO: 高斯测试参数配置:
2025-06-05 11:05:17,074  INFO:   SYM_RATES: [0]
2025-06-05 11:05:17,074  INFO:   S_CODE_EN: [1]
2025-06-05 11:05:17,076  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-05 11:05:17,077  INFO:   SNRS: [1, 2, 3, 4, 5, 6]
2025-06-05 11:05:17,077  INFO:   DATA_LEN: [6]
2025-06-05 11:05:17,079  INFO: 测试用例数量(36)超过限制(10)，将只执行前10个用例
2025-06-05 11:05:17,084  INFO: 成功生成 10 个高斯测试用例
2025-06-05 11:05:17,087  INFO: 总测试用例数: 10
2025-06-05 11:05:17,088  INFO: 
=== 执行测试用例 1/10 ===
2025-06-05 11:05:17,089  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 1, 'signal_ch': 0}
2025-06-05 11:05:17,089  INFO: 开始配置CMW信号发生器...
2025-06-05 11:05:18,080  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-05 11:05:18,081  INFO: 开始加载ARB文件...
2025-06-05 11:05:18,303  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-05 11:06:28,707  INFO: 初始化RF板...
2025-06-05 11:06:29,025  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-05 11:06:30,700  ERROR: 测试用例 1 执行失败: sagitta_dut_parse_read_data() got an unexpected keyword argument 'interval_time'
2025-06-05 11:06:30,745  INFO: 
=== 执行测试用例 2/10 ===
2025-06-05 11:06:30,745  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 2, 'signal_ch': 0}
2025-06-05 11:06:30,746  INFO: 开始配置CMW信号发生器...
2025-06-05 11:06:35,691  INFO: 测试停止!
2025-06-05 11:07:33,468  INFO: 测试开始!
2025-06-05 11:07:33,470  INFO: 测试进度：0/1
2025-06-05 11:07:33,471  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-05 11:07:33,471  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-05 11:07:33,507  INFO: === 初始化测试环境 ===
2025-06-05 11:07:33,507  INFO: 正在初始化设备连接...
2025-06-05 11:07:33,555  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-05 11:07:33,567  INFO: 正在创建测试报告...
2025-06-05 11:07:33,567  INFO: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告
2025-06-05 11:07:33,568  INFO: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250605_110733
2025-06-05 11:07:33,568  INFO: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250605_110733/superlink_rx_gauss_report_20250605_110733.xlsx
2025-06-05 11:07:33,569  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250605_110733/superlink_rx_gauss_report_20250605_110733.xlsx
2025-06-05 11:07:33,572  INFO: === 开始高斯测试 ===
2025-06-05 11:07:33,573  INFO: === 生成高斯测试用例 ===
2025-06-05 11:07:33,574  INFO: 高斯测试参数配置:
2025-06-05 11:07:33,574  INFO:   SYM_RATES: [0]
2025-06-05 11:07:33,574  INFO:   S_CODE_EN: [1]
2025-06-05 11:07:33,575  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-05 11:07:33,575  INFO:   SNRS: [1, 2, 3, 4, 5, 6]
2025-06-05 11:07:33,575  INFO:   DATA_LEN: [6]
2025-06-05 11:07:33,575  INFO: 测试用例数量(36)超过限制(10)，将只执行前10个用例
2025-06-05 11:07:33,576  INFO: 成功生成 10 个高斯测试用例
2025-06-05 11:07:33,576  INFO: 总测试用例数: 10
2025-06-05 11:07:33,576  INFO: 
=== 执行测试用例 1/10 ===
2025-06-05 11:07:33,577  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 1, 'signal_ch': 0}
2025-06-05 11:07:33,584  INFO: 开始配置CMW信号发生器...
2025-06-05 11:07:34,686  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-05 11:07:34,687  INFO: 开始加载ARB文件...
2025-06-05 11:07:34,917  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-05 11:07:35,126  INFO: 初始化RF板...
2025-06-05 11:07:35,444  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-05 11:08:18,457  INFO: 测试成功 - PER: 32.12159377146783%, 极性误差: 7.6904e-03~5.6152e-02
2025-06-05 11:08:18,500  INFO: 
=== 执行测试用例 2/10 ===
2025-06-05 11:08:18,501  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 2, 'signal_ch': 0}
2025-06-05 11:08:18,503  INFO: 开始配置CMW信号发生器...
2025-06-05 11:08:24,536  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-05 11:08:24,537  INFO: 开始加载ARB文件...
2025-06-05 11:08:24,767  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym1db_grdc.wv"
2025-06-05 11:09:35,182  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-05 11:10:06,017  INFO: 测试成功 - PER: 12.126285577613016%, 极性误差: 1.7090e-03~1.0986e-02
2025-06-05 11:10:06,049  INFO: 
=== 执行测试用例 3/10 ===
2025-06-05 11:10:06,049  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 3, 'signal_ch': 0}
2025-06-05 11:10:06,050  INFO: 开始配置CMW信号发生器...
2025-06-05 11:10:12,082  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-05 11:10:12,082  INFO: 开始加载ARB文件...
2025-06-05 11:10:12,302  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym2db_grdc.wv"
2025-06-05 11:11:23,882  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-05 11:12:24,455  INFO: 测试成功 - PER: 3.3485301053293504%, 极性误差: 6.1035e-04~1.9531e-03
2025-06-05 11:12:24,489  INFO: 
=== 执行测试用例 4/10 ===
2025-06-05 11:12:24,490  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 4, 'signal_ch': 0}
2025-06-05 11:12:24,492  INFO: 开始配置CMW信号发生器...
2025-06-05 11:12:30,518  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-05 11:12:30,519  INFO: 开始加载ARB文件...
2025-06-05 11:12:30,741  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym3db_grdc.wv"
2025-06-05 11:14:02,947  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-05 11:20:44,481  ERROR: 测试失败: min() arg is an empty sequence
2025-06-05 11:20:44,610  INFO: 
=== 执行测试用例 5/10 ===
2025-06-05 11:20:44,611  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 5, 'signal_ch': 0}
2025-06-05 11:20:44,612  INFO: 开始配置CMW信号发生器...
2025-06-05 11:21:08,832  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-05 11:21:08,833  INFO: 开始加载ARB文件...
2025-06-05 11:21:11,858  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym4db_grdc.wv"
2025-06-05 11:22:40,125  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-05 11:24:39,049  ERROR: 测试失败: min() arg is an empty sequence
2025-06-05 11:24:39,142  INFO: 
=== 执行测试用例 6/10 ===
2025-06-05 11:24:39,144  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 6, 'signal_ch': 0}
2025-06-05 11:24:39,145  INFO: 开始配置CMW信号发生器...
2025-06-05 11:24:45,340  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-05 11:24:45,340  INFO: 开始加载ARB文件...
2025-06-05 11:24:45,607  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym5db_grdc.wv"
2025-06-05 11:25:01,286  INFO: 测试停止!
2025-06-05 11:32:00,739  INFO: 测试开始!
2025-06-05 11:32:00,742  INFO: 测试进度：0/1
2025-06-05 11:32:00,744  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-05 11:32:00,744  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-05 11:32:00,776  INFO: === 初始化测试环境 ===
2025-06-05 11:32:00,782  INFO: 正在初始化设备连接...
2025-06-05 11:32:00,849  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-05 11:32:00,857  INFO: 正在创建测试报告...
2025-06-05 11:32:00,857  INFO: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告
2025-06-05 11:32:00,858  INFO: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250605_113200
2025-06-05 11:32:00,859  INFO: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250605_113200/superlink_rx_gauss_report_20250605_113200.xlsx
2025-06-05 11:32:00,865  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250605_113200/superlink_rx_gauss_report_20250605_113200.xlsx
2025-06-05 11:32:00,865  INFO: === 开始高斯测试 ===
2025-06-05 11:32:00,865  INFO: === 生成高斯测试用例 ===
2025-06-05 11:32:00,865  INFO: 高斯测试参数配置:
2025-06-05 11:32:00,868  INFO:   SYM_RATES: [0]
2025-06-05 11:32:00,869  INFO:   S_CODE_EN: [1]
2025-06-05 11:32:00,869  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-05 11:32:00,869  INFO:   SNRS: [1, 2, 3, 4, 5, 6]
2025-06-05 11:32:00,869  INFO:   DATA_LEN: [6]
2025-06-05 11:32:00,869  INFO: 测试用例数量(36)超过限制(10)，将只执行前10个用例
2025-06-05 11:32:00,869  INFO: 成功生成 10 个高斯测试用例
2025-06-05 11:32:00,870  INFO: 总测试用例数: 10
2025-06-05 11:32:00,870  INFO: 
=== 执行测试用例 1/10 ===
2025-06-05 11:32:00,870  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 1, 'signal_ch': 0}
2025-06-05 11:32:00,870  INFO: 开始配置CMW信号发生器...
2025-06-05 11:32:06,894  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-05 11:32:06,894  INFO: 开始加载ARB文件...
2025-06-05 11:32:07,130  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-05 11:33:17,540  INFO: 初始化RF板...
2025-06-05 11:33:17,863  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-05 11:34:00,652  INFO: 测试成功 - PER: 32.280355380059234%, 极性误差: 9.1553e-03~3.8086e-02
2025-06-05 11:34:00,692  INFO: 
=== 执行测试用例 2/10 ===
2025-06-05 11:34:00,693  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 2, 'signal_ch': 0}
2025-06-05 11:34:00,697  INFO: 开始配置CMW信号发生器...
2025-06-05 11:34:06,720  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-05 11:34:06,721  INFO: 开始加载ARB文件...
2025-06-05 11:34:06,941  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym1db_grdc.wv"
2025-06-05 11:35:17,339  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-05 11:35:48,196  INFO: 测试成功 - PER: 12.560732775786542%, 极性误差: 2.9297e-03~7.9346e-03
2025-06-05 11:35:48,230  INFO: 
=== 执行测试用例 3/10 ===
2025-06-05 11:35:48,230  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 3, 'signal_ch': 0}
2025-06-05 11:35:48,232  INFO: 开始配置CMW信号发生器...
2025-06-05 11:35:54,257  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-05 11:35:54,258  INFO: 开始加载ARB文件...
2025-06-05 11:35:54,479  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym2db_grdc.wv"
2025-06-05 11:37:04,879  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-05 11:37:35,831  INFO: 测试成功 - PER: 3.5865974516281263%, 极性误差: 1.2207e-03~1.2207e-02
2025-06-05 11:37:35,862  INFO: 
=== 执行测试用例 4/10 ===
2025-06-05 11:37:35,863  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 4, 'signal_ch': 0}
2025-06-05 11:37:35,863  INFO: 开始配置CMW信号发生器...
2025-06-05 11:37:41,884  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-05 11:37:41,886  INFO: 开始加载ARB文件...
2025-06-05 11:37:42,107  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym3db_grdc.wv"
2025-06-05 11:38:52,557  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-05 11:39:18,105  INFO: 测试成功 - PER: 0.4547592912027554%, 极性误差: 0.0000e+00~8.5449e-04
2025-06-05 11:39:18,148  INFO: 
=== 执行测试用例 5/10 ===
2025-06-05 11:39:18,149  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 5, 'signal_ch': 0}
2025-06-05 11:39:18,151  INFO: 开始配置CMW信号发生器...
2025-06-05 11:39:24,231  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-05 11:39:24,232  INFO: 开始加载ARB文件...
2025-06-05 11:39:24,461  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym4db_grdc.wv"
2025-06-05 11:40:34,918  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-05 11:41:00,435  INFO: 测试成功 - PER: 0.00786101721562682%, 极性误差: 0.0000e+00~0.0000e+00
2025-06-05 11:41:00,489  INFO: 
=== 执行测试用例 6/10 ===
2025-06-05 11:41:00,489  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 6, 'signal_ch': 0}
2025-06-05 11:41:00,491  INFO: 开始配置CMW信号发生器...
2025-06-05 11:41:06,538  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-05 11:41:06,539  INFO: 开始加载ARB文件...
2025-06-05 11:41:06,769  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym5db_grdc.wv"
2025-06-05 11:42:17,176  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-05 11:42:42,672  INFO: 测试成功 - PER: 0.0%, 极性误差: 0.0000e+00~0.0000e+00
2025-06-05 11:42:42,730  INFO: 
=== 执行测试用例 7/10 ===
2025-06-05 11:42:42,730  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 3, 'snr': 1, 'signal_ch': 0}
2025-06-05 11:42:42,732  INFO: 开始配置CMW信号发生器...
2025-06-05 11:42:48,895  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-05 11:42:48,895  INFO: 开始加载ARB文件...
2025-06-05 11:42:48,909  ERROR: ARB文件不存在: ARB 文件不存在
2025-06-05 11:42:48,969  INFO: 
=== 执行测试用例 8/10 ===
2025-06-05 11:42:48,970  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 3, 'snr': 2, 'signal_ch': 0}
2025-06-05 11:42:48,971  INFO: 开始配置CMW信号发生器...
2025-06-05 11:42:49,985  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-05 11:42:49,985  INFO: 开始加载ARB文件...
2025-06-05 11:42:50,207  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar3_8\qpsk_1Rs_24Fs_1000fps_PType1_seed0_5bk_sym1db_grdc.wv"
2025-06-05 11:44:00,679  INFO: 发送测试命令: bb test_cmw 0 0 1 3 6
2025-06-05 11:44:31,689  INFO: 测试成功 - PER: 19.89624900239425%, 极性误差: 2.1240e-02~3.0884e-02
2025-06-05 11:44:31,729  INFO: 
=== 执行测试用例 9/10 ===
2025-06-05 11:44:31,731  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 3, 'snr': 3, 'signal_ch': 0}
2025-06-05 11:44:31,731  INFO: 开始配置CMW信号发生器...
2025-06-05 11:44:37,752  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-05 11:44:37,752  INFO: 开始加载ARB文件...
2025-06-05 11:44:37,973  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar3_8\qpsk_1Rs_24Fs_1000fps_PType1_seed0_5bk_sym2db_grdc.wv"
2025-06-05 11:45:48,589  INFO: 发送测试命令: bb test_cmw 0 0 1 3 6
2025-06-05 11:46:19,573  INFO: 测试成功 - PER: 4.557577659240664%, 极性误差: 2.5635e-03~9.5215e-03
2025-06-05 11:46:19,613  INFO: 
=== 执行测试用例 10/10 ===
2025-06-05 11:46:19,613  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 3, 'snr': 4, 'signal_ch': 0}
2025-06-05 11:46:19,614  INFO: 开始配置CMW信号发生器...
2025-06-05 11:46:25,639  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-05 11:46:25,640  INFO: 开始加载ARB文件...
2025-06-05 11:46:25,862  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar3_8\qpsk_1Rs_24Fs_1000fps_PType1_seed0_5bk_sym3db_grdc.wv"
2025-06-05 11:47:36,340  INFO: 发送测试命令: bb test_cmw 0 0 1 3 6
2025-06-05 11:48:02,056  INFO: 测试成功 - PER: 0.8979023144206177%, 极性误差: 7.3242e-04~1.2207e-03
2025-06-05 11:48:02,098  INFO: === 高斯测试完成 ===
2025-06-05 11:48:02,100  INFO: === 清理测试环境 ===
2025-06-05 11:48:02,103  INFO: 设备连接已断开
2025-06-05 11:48:02,103  INFO: === 测试完成 ===
2025-06-05 11:48:02,103  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-06-05 11:48:02,104  INFO: 测试进度：1/1
2025-06-05 11:48:07,107  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-06-05 11:48:07,110  INFO: 测试完成！总共测试耗时：00:16:02
2025-06-05 14:44:42,223  INFO: 测试开始!
2025-06-05 14:44:42,225  INFO: 测试进度：0/1
2025-06-05 14:44:42,226  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-05 14:44:42,227  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-05 14:44:42,271  INFO: === 初始化测试环境 ===
2025-06-05 14:44:42,273  INFO: 正在初始化设备连接...
2025-06-05 14:44:42,335  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-05 14:44:42,346  INFO: 正在创建测试报告...
2025-06-05 14:44:42,346  INFO: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告
2025-06-05 14:44:42,347  INFO: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250605_144442
2025-06-05 14:44:42,349  INFO: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250605_144442/superlink_rx_gauss_report_20250605_144442.xlsx
2025-06-05 14:44:42,349  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250605_144442/superlink_rx_gauss_report_20250605_144442.xlsx
2025-06-05 14:44:42,350  INFO: === 开始高斯测试 ===
2025-06-05 14:44:42,352  INFO: === 生成高斯测试用例 ===
2025-06-05 14:44:42,357  INFO: 高斯测试参数配置:
2025-06-05 14:44:42,358  INFO:   SYM_RATES: [0]
2025-06-05 14:44:42,358  INFO:   S_CODE_EN: [1]
2025-06-05 14:44:42,358  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-05 14:44:42,358  INFO:   SNRS: [1, 2, 3, 4, 5, 6]
2025-06-05 14:44:42,359  INFO:   DATA_LEN: [6]
2025-06-05 14:44:42,359  INFO: 测试用例数量(36)超过限制(2)，将只执行前2个用例
2025-06-05 14:44:42,359  INFO: 成功生成 2 个高斯测试用例
2025-06-05 14:44:42,359  INFO: 总测试用例数: 2
2025-06-05 14:44:42,360  INFO: 
=== 执行测试用例 1/2 ===
2025-06-05 14:44:42,360  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 1, 'signal_ch': 0}
2025-06-05 14:44:42,361  INFO: 开始配置CMW信号发生器...
2025-06-05 14:44:43,356  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-05 14:44:43,361  INFO: 开始加载ARB文件...
2025-06-05 14:44:43,599  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-05 14:45:54,008  INFO: 初始化RF板...
2025-06-05 14:45:54,119  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-05 14:45:54,128  ERROR: 测试用例 1 执行失败: 'SerialPortCommunication' object has no attribute 'sagitta_dut_get_gauss_result'
2025-06-05 14:45:54,174  INFO: 
=== 执行测试用例 2/2 ===
2025-06-05 14:45:54,175  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 2, 'signal_ch': 0}
2025-06-05 14:45:54,177  INFO: 开始配置CMW信号发生器...
2025-06-05 14:46:00,194  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-05 14:46:00,195  INFO: 开始加载ARB文件...
2025-06-05 14:46:00,417  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym1db_grdc.wv"
2025-06-05 14:46:02,464  INFO: 测试停止!
2025-06-05 14:47:57,915  INFO: 测试开始!
2025-06-05 14:47:57,921  INFO: 测试进度：0/1
2025-06-05 14:47:57,921  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-05 14:47:57,922  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-05 14:47:57,945  INFO: === 初始化测试环境 ===
2025-06-05 14:47:57,946  INFO: 正在初始化设备连接...
2025-06-05 14:47:58,010  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-05 14:47:58,018  INFO: 正在创建测试报告...
2025-06-05 14:47:58,019  INFO: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告
2025-06-05 14:47:58,020  INFO: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250605_144758
2025-06-05 14:47:58,020  INFO: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250605_144758/superlink_rx_gauss_report_20250605_144758.xlsx
2025-06-05 14:47:58,021  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250605_144758/superlink_rx_gauss_report_20250605_144758.xlsx
2025-06-05 14:47:58,021  INFO: === 开始高斯测试 ===
2025-06-05 14:47:58,022  INFO: === 生成高斯测试用例 ===
2025-06-05 14:47:58,022  INFO: 高斯测试参数配置:
2025-06-05 14:47:58,022  INFO:   SYM_RATES: [0]
2025-06-05 14:47:58,026  INFO:   S_CODE_EN: [1]
2025-06-05 14:47:58,026  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-05 14:47:58,027  INFO:   SNRS: [1, 2, 3, 4, 5, 6]
2025-06-05 14:47:58,027  INFO:   DATA_LEN: [6]
2025-06-05 14:47:58,027  INFO: 测试用例数量(36)超过限制(2)，将只执行前2个用例
2025-06-05 14:47:58,027  INFO: 成功生成 2 个高斯测试用例
2025-06-05 14:47:58,027  INFO: 总测试用例数: 2
2025-06-05 14:47:58,028  INFO: 
=== 执行测试用例 1/2 ===
2025-06-05 14:47:58,028  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 1, 'signal_ch': 0}
2025-06-05 14:47:58,028  INFO: 开始配置CMW信号发生器...
2025-06-05 14:48:04,057  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-05 14:48:04,060  INFO: 开始加载ARB文件...
2025-06-05 14:48:04,289  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-05 14:49:14,702  INFO: 初始化RF板...
2025-06-05 14:49:14,809  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-05 14:49:21,153  ERROR: 测试失败: rsv data failed! cmd_info: bb test_rd

2025-06-05 14:49:21,200  INFO: 
=== 执行测试用例 2/2 ===
2025-06-05 14:49:21,200  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 2, 'signal_ch': 0}
2025-06-05 14:49:21,202  INFO: 开始配置CMW信号发生器...
2025-06-05 14:49:27,235  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-05 14:49:27,236  INFO: 开始加载ARB文件...
2025-06-05 14:49:27,468  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym1db_grdc.wv"
2025-06-05 14:49:28,516  INFO: 测试停止!
