2025-05-30 10:01:14,514  INFO: 测试开始!
2025-05-30 10:01:14,515  INFO: 测试进度：0/1
2025-05-30 10:01:14,515  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-30 10:01:14,516  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-30 10:01:14,621  INFO: bluetooth_tester host_id: <PERSON><PERSON><PERSON>&<PERSON>,CMW,1201.0002k75/101147,3.7.171
2025-05-30 10:01:14,636  INFO: 开始测试
2025-05-30 10:01:17,756  INFO: Generator State: OFF
2025-05-30 10:01:18,565  INFO: level: INV
2025-05-30 10:01:53,981  ERROR: 测试终止！测试错误：测试用例执行异常( timed out )
2025-05-30 10:02:27,410  INFO: 测试开始!
2025-05-30 10:02:27,414  INFO: 测试进度：0/1
2025-05-30 10:02:27,415  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-30 10:02:27,415  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-30 10:02:27,502  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-30 10:02:27,513  INFO: 开始测试
2025-05-30 10:02:27,724  INFO: Generator State: OFF
2025-05-30 10:02:28,532  INFO: level: INV
2025-05-30 10:02:33,948  ERROR: 测试终止！测试错误：测试用例执行异常( timed out )
2025-05-30 10:07:16,672  INFO: 测试开始!
2025-05-30 10:07:16,673  INFO: 测试进度：0/1
2025-05-30 10:07:16,673  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-30 10:07:16,673  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-30 10:07:16,752  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-30 10:07:16,762  INFO: 开始测试
2025-05-30 10:07:16,984  INFO: Generator State: OFF
2025-05-30 10:07:17,802  INFO: level: INV
2025-05-30 10:07:18,223  INFO: file: "No File Selected"
2025-05-30 10:07:18,224  INFO: 测试结束
2025-05-30 10:07:18,227  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-05-30 10:07:18,227  INFO: 测试进度：1/1
2025-05-30 10:07:23,230  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-05-30 10:07:23,234  INFO: 测试完成！总共测试耗时：00:00:06
2025-05-30 10:10:12,309  INFO: 测试开始!
2025-05-30 10:10:12,314  INFO: 测试进度：0/1
2025-05-30 10:10:12,315  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-30 10:10:12,315  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-30 10:10:12,422  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-30 10:10:12,431  INFO: 开始测试
2025-05-30 10:10:12,648  INFO: Generator State: OFF
2025-05-30 10:10:13,456  INFO: level: INV
2025-05-30 10:10:13,868  INFO: file: "No File Selected"
2025-05-30 10:10:13,869  INFO: 测试结束
2025-05-30 10:10:13,871  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-05-30 10:10:13,873  INFO: 测试进度：1/1
2025-05-30 10:10:18,872  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-05-30 10:10:18,876  INFO: 测试完成！总共测试耗时：00:00:07
2025-05-30 10:17:46,510  INFO: 测试开始!
2025-05-30 10:17:46,511  INFO: 测试进度：0/1
2025-05-30 10:17:46,511  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-30 10:17:46,512  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-30 10:17:46,665  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-30 10:17:46,674  INFO: 开始测试
2025-05-30 10:17:46,688  INFO: Generator State: ON
2025-05-30 10:17:46,689  INFO: 测试结束
2025-05-30 10:17:46,692  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-05-30 10:17:46,692  INFO: 测试进度：1/1
2025-05-30 10:17:51,694  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-05-30 10:17:51,700  INFO: 测试完成！总共测试耗时：00:00:05
2025-05-30 10:19:17,039  INFO: 测试开始!
2025-05-30 10:19:17,040  INFO: 测试进度：0/1
2025-05-30 10:19:17,041  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-30 10:19:17,042  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-30 10:19:17,143  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-30 10:19:17,151  INFO: 开始测试
2025-05-30 10:19:17,164  INFO: Generator State: PEND
2025-05-30 10:19:17,165  INFO: 测试结束
2025-05-30 10:19:17,167  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-05-30 10:19:17,168  INFO: 测试进度：1/1
2025-05-30 10:19:22,173  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-05-30 10:19:22,176  INFO: 测试完成！总共测试耗时：00:00:05
2025-05-30 10:19:26,207  INFO: 测试开始!
2025-05-30 10:19:26,209  INFO: 测试进度：0/1
2025-05-30 10:19:26,211  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-30 10:19:26,213  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-30 10:19:26,336  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-30 10:19:26,346  INFO: 开始测试
2025-05-30 10:19:26,357  INFO: Generator State: PEND
2025-05-30 10:19:26,358  INFO: 测试结束
2025-05-30 10:19:26,362  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-05-30 10:19:26,362  INFO: 测试进度：1/1
2025-05-30 10:19:31,362  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-05-30 10:19:31,365  INFO: 测试完成！总共测试耗时：00:00:05
2025-05-30 10:21:59,843  INFO: 测试开始!
2025-05-30 10:21:59,846  INFO: 测试进度：0/1
2025-05-30 10:21:59,849  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-30 10:21:59,849  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-30 10:21:59,937  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-30 10:21:59,947  INFO: 开始测试
2025-05-30 10:22:03,242  INFO: Generator State: OFF
2025-05-30 10:22:04,059  INFO: level: INV
2025-05-30 10:22:04,480  INFO: file: "No File Selected"
2025-05-30 10:22:04,482  INFO: 测试结束
2025-05-30 10:22:04,484  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-05-30 10:22:04,484  INFO: 测试进度：1/1
2025-05-30 10:22:09,489  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-05-30 10:22:09,494  INFO: 测试完成！总共测试耗时：00:00:09
2025-05-30 10:24:40,402  INFO: 测试开始!
2025-05-30 10:24:40,405  INFO: 测试进度：0/1
2025-05-30 10:24:40,408  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-30 10:24:40,409  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-30 10:24:40,499  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-30 10:24:40,510  INFO: 开始测试
2025-05-30 10:24:40,524  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar4_8\qpsk_1Rs_24Fs_1000fps_PType2_seed0_4bk_sym3db_grdc.wv"
2025-05-30 10:24:40,527  INFO: 测试结束
2025-05-30 10:24:40,529  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-05-30 10:24:40,529  INFO: 测试进度：1/1
2025-05-30 10:24:45,531  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-05-30 10:24:45,535  INFO: 测试完成！总共测试耗时：00:00:05
2025-05-30 10:26:10,065  INFO: 测试开始!
2025-05-30 10:26:10,066  INFO: 测试进度：0/1
2025-05-30 10:26:10,070  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-30 10:26:10,070  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-30 10:26:10,157  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-30 10:26:10,167  INFO: 开始测试
2025-05-30 10:26:10,383  INFO: Generator State: OFF
2025-05-30 10:26:11,200  INFO: level: INV
2025-05-30 10:26:11,612  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar4_8\qpsk_1Rs_24Fs_1000fps_PType2_seed0_4bk_sym1db_grdc.wv"
2025-05-30 10:26:11,614  INFO: 测试结束
2025-05-30 10:26:11,616  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-05-30 10:26:11,616  INFO: 测试进度：1/1
2025-05-30 10:26:16,616  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-05-30 10:26:16,619  INFO: 测试完成！总共测试耗时：00:00:07
2025-05-30 10:33:18,471  INFO: 测试开始!
2025-05-30 10:33:18,473  INFO: 测试进度：0/1
2025-05-30 10:33:18,473  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-30 10:33:18,473  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-30 10:33:18,539  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-30 10:33:18,548  INFO: 开始测试
2025-05-30 10:33:18,757  INFO: Generator State: OFF
2025-05-30 10:33:19,568  INFO: level: INV
2025-05-30 10:33:19,991  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar4_8\qpsk_1Rs_24Fs_1000fps_PType2_seed0_4bk_sym1db_grdc.wv"
2025-05-30 10:33:25,012  ERROR: 测试终止！测试错误：测试用例执行异常( timed out )
2025-05-30 10:35:06,026  INFO: 测试开始!
2025-05-30 10:35:06,027  INFO: 测试进度：0/1
2025-05-30 10:35:06,030  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-30 10:35:06,030  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-30 10:35:06,117  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-30 10:35:06,126  INFO: 开始测试
2025-05-30 10:35:06,333  INFO: Generator State: OFF
2025-05-30 10:35:07,145  INFO: level: INV
2025-05-30 10:35:07,553  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar4_8\qpsk_1Rs_24Fs_1000fps_PType2_seed0_4bk_sym1db_grdc.wv"
2025-05-30 10:35:07,752  INFO: 测试结束
2025-05-30 10:35:07,755  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-05-30 10:35:07,755  INFO: 测试进度：1/1
2025-05-30 10:35:12,757  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-05-30 10:35:12,760  INFO: 测试完成！总共测试耗时：00:00:07
2025-05-30 10:35:51,670  INFO: 测试开始!
2025-05-30 10:35:51,672  INFO: 测试进度：0/1
2025-05-30 10:35:51,673  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-30 10:35:51,673  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-30 10:35:51,756  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-30 10:35:51,764  INFO: 开始测试
2025-05-30 10:35:51,983  INFO: Generator State: OFF
2025-05-30 10:35:52,833  INFO: level: INV
2025-05-30 10:35:53,262  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar4_8\qpsk_1Rs_24Fs_1000fps_PType2_seed0_4bk_sym1db_grdc.wv"
2025-05-30 10:35:53,471  INFO: 测试结束
2025-05-30 10:35:53,474  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-05-30 10:35:53,474  INFO: 测试进度：1/1
2025-05-30 10:35:58,480  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-05-30 10:35:58,483  INFO: 测试完成！总共测试耗时：00:00:06
2025-05-30 10:39:00,493  INFO: 测试开始!
2025-05-30 10:39:00,494  INFO: 测试进度：0/1
2025-05-30 10:39:00,494  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-30 10:39:00,498  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-30 10:39:00,590  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-30 10:39:00,598  INFO: 开始测试
2025-05-30 10:39:00,611  INFO: Generator State: OFF
2025-05-30 10:39:01,627  INFO: level: INV
2025-05-30 10:39:02,047  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar4_8\qpsk_1Rs_24Fs_1000fps_PType2_seed0_4bk_sym1db_grdc.wv"
2025-05-30 10:39:02,254  INFO: 测试结束
2025-05-30 10:39:02,258  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-05-30 10:39:02,258  INFO: 测试进度：1/1
2025-05-30 10:39:07,259  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-05-30 10:39:07,265  INFO: 测试完成！总共测试耗时：00:00:07
2025-05-30 10:41:47,061  INFO: 测试开始!
2025-05-30 10:41:47,063  INFO: 测试进度：0/1
2025-05-30 10:41:47,063  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-30 10:41:47,063  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-30 10:41:47,152  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-30 10:41:47,160  INFO: 开始测试
2025-05-30 10:41:47,172  INFO: Generator State: OFF
2025-05-30 10:41:49,191  INFO: level: INV
2025-05-30 10:41:49,599  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar4_8\qpsk_1Rs_24Fs_1000fps_PType2_seed0_4bk_sym1db_grdc.wv"
2025-05-30 10:41:49,798  INFO: 测试结束
2025-05-30 10:41:49,802  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-05-30 10:41:49,802  INFO: 测试进度：1/1
2025-05-30 10:41:54,802  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-05-30 10:41:54,806  INFO: 测试完成！总共测试耗时：00:00:07
2025-05-30 10:42:53,543  INFO: 测试开始!
2025-05-30 10:42:53,547  INFO: 测试进度：0/1
2025-05-30 10:42:53,549  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-30 10:42:53,549  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-30 10:42:53,635  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-30 10:42:53,643  INFO: 开始测试
2025-05-30 10:42:53,654  INFO: Generator State: ON
2025-05-30 10:42:55,689  INFO: level: INV
2025-05-30 10:42:56,098  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar4_8\qpsk_1Rs_24Fs_1000fps_PType2_seed0_4bk_sym2db_grdc.wv"
2025-05-30 10:44:02,251  INFO: 测试结束
2025-05-30 10:44:02,255  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-05-30 10:44:02,256  INFO: 测试进度：1/1
2025-05-30 10:44:07,258  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-05-30 10:44:07,262  INFO: 测试完成！总共测试耗时：00:01:14
2025-05-30 11:03:49,601  INFO: 测试开始!
2025-05-30 11:03:49,602  INFO: 测试进度：0/1
2025-05-30 11:03:49,603  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-30 11:03:49,604  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-30 11:03:49,711  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-30 11:03:49,720  INFO: 开始测试
2025-05-30 11:03:49,734  INFO: Generator State: ON
2025-05-30 11:03:51,805  INFO: level: INV
2025-05-30 11:03:52,235  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar4_8\qpsk_1Rs_24Fs_1000fps_PType2_seed0_4bk_sym2db_grdc.wv"
2025-05-30 11:03:53,751  INFO: 测试结束
2025-05-30 11:03:53,754  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-05-30 11:03:53,755  INFO: 测试进度：1/1
2025-05-30 11:03:58,754  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-05-30 11:03:58,767  INFO: 测试完成！总共测试耗时：00:00:10
2025-05-30 13:16:42,943  INFO: 测试开始!
2025-05-30 13:16:42,945  INFO: 测试进度：0/1
2025-05-30 13:16:42,947  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-30 13:16:42,947  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-30 13:16:43,047  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-30 13:16:43,058  INFO: 开始测试
2025-05-30 13:16:44,048  INFO: 测试结束
2025-05-30 13:16:44,051  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-05-30 13:16:44,052  INFO: 测试进度：1/1
2025-05-30 13:16:49,054  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-05-30 13:16:49,058  INFO: 测试完成！总共测试耗时：00:00:06
2025-05-30 13:19:01,350  INFO: 测试开始!
2025-05-30 13:19:01,352  INFO: 测试进度：0/1
2025-05-30 13:19:01,352  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-30 13:19:01,353  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-30 13:19:01,445  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-30 13:19:01,454  INFO: 开始测试
2025-05-30 13:19:02,844  INFO: 测试结束
2025-05-30 13:19:02,847  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-05-30 13:19:02,848  INFO: 测试进度：1/1
2025-05-30 13:19:07,852  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-05-30 13:19:07,857  INFO: 测试完成！总共测试耗时：00:00:07
2025-05-30 13:30:21,438  INFO: 测试开始!
2025-05-30 13:30:21,441  INFO: 测试进度：0/1
2025-05-30 13:30:21,442  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-30 13:30:21,442  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-30 13:30:21,539  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-30 13:30:21,548  INFO: 开始测试
2025-05-30 13:30:22,615  INFO: 测试结束
2025-05-30 13:30:22,618  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-05-30 13:30:22,619  INFO: 测试进度：1/1
2025-05-30 13:30:27,619  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-05-30 13:30:27,623  INFO: 测试完成！总共测试耗时：00:00:06
2025-05-30 13:41:29,238  INFO: 测试开始!
2025-05-30 13:41:29,241  INFO: 测试进度：0/1
2025-05-30 13:41:29,241  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-30 13:41:29,241  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-30 13:41:29,335  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-30 13:41:29,343  INFO: 开始测试
2025-05-30 13:41:46,726  INFO: 测试结束
2025-05-30 13:41:46,728  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-05-30 13:41:46,729  INFO: 测试进度：1/1
2025-05-30 13:41:51,735  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-05-30 13:41:51,741  INFO: 测试完成！总共测试耗时：00:00:23
2025-05-30 13:48:47,856  INFO: 测试开始!
2025-05-30 13:48:47,859  INFO: 测试进度：0/1
2025-05-30 13:48:47,860  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-30 13:48:47,861  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-30 13:48:47,960  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-30 13:48:47,968  INFO: 开始测试
2025-05-30 13:49:45,319  INFO: 测试停止!
2025-05-30 14:18:34,477  INFO: 测试开始!
2025-05-30 14:18:34,479  INFO: 测试进度：0/1
2025-05-30 14:18:34,479  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-30 14:18:34,480  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-30 14:18:34,583  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-30 14:18:34,589  INFO: 开始测试
2025-05-30 14:18:34,716  INFO: 测试结束
2025-05-30 14:18:34,717  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-05-30 14:18:34,718  INFO: 测试进度：1/1
2025-05-30 14:18:39,721  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-05-30 14:18:39,724  INFO: 测试完成！总共测试耗时：00:00:05
2025-05-30 14:22:39,260  INFO: 测试开始!
2025-05-30 14:22:39,262  INFO: 测试进度：0/1
2025-05-30 14:22:39,263  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-30 14:22:39,263  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-30 14:22:39,364  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-30 14:22:39,370  INFO: 开始测试
2025-05-30 14:22:39,804  INFO: 测试结束
2025-05-30 14:22:39,804  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-05-30 14:22:39,805  INFO: 测试进度：1/1
2025-05-30 14:22:44,805  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-05-30 14:22:44,809  INFO: 测试完成！总共测试耗时：00:00:05
2025-05-30 14:23:24,047  INFO: 测试开始!
2025-05-30 14:23:24,048  INFO: 测试进度：0/1
2025-05-30 14:23:24,049  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-30 14:23:24,050  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-30 14:23:24,135  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-30 14:23:24,144  INFO: 开始测试
2025-05-30 14:25:55,013  INFO: 测试停止!
2025-05-30 14:28:54,153  INFO: 测试开始!
2025-05-30 14:28:54,154  INFO: 测试进度：0/1
2025-05-30 14:28:54,156  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-30 14:28:54,156  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-30 14:28:54,257  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-30 14:28:54,271  INFO: 开始测试
2025-05-30 14:28:54,281  INFO: Generator State: OFF
2025-05-30 14:29:54,004  INFO: 测试停止!
2025-05-30 16:31:27,197  INFO: 测试开始!
2025-05-30 16:31:27,199  INFO: 测试进度：0/1
2025-05-30 16:31:27,200  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-30 16:31:27,200  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-30 16:31:27,308  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-30 16:31:27,318  INFO: 开始测试
2025-05-30 16:31:27,329  INFO: Generator State: ON
2025-05-30 16:31:41,533  INFO: 测试结束
2025-05-30 16:31:41,536  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-05-30 16:31:41,537  INFO: 测试进度：1/1
2025-05-30 16:31:46,537  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-05-30 16:31:46,539  INFO: 测试完成！总共测试耗时：00:00:19
2025-05-30 16:55:02,938  INFO: 测试开始!
2025-05-30 16:55:02,940  INFO: 测试进度：0/1
2025-05-30 16:55:02,940  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-30 16:55:02,940  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-30 16:55:03,033  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-30 16:55:03,042  INFO: 开始测试
2025-05-30 16:55:03,054  INFO: Generator State: ON
2025-05-30 16:55:17,171  INFO: 测试结束
2025-05-30 16:55:17,174  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-05-30 16:55:17,174  INFO: 测试进度：1/1
2025-05-30 16:55:22,175  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-05-30 16:55:22,178  INFO: 测试完成！总共测试耗时：00:00:19
2025-05-30 16:59:19,245  INFO: 测试开始!
2025-05-30 16:59:19,248  INFO: 测试进度：0/1
2025-05-30 16:59:19,249  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-30 16:59:19,253  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-30 16:59:19,335  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-30 16:59:19,342  INFO: 开始测试
2025-05-30 16:59:19,354  INFO: Generator State: ON
2025-05-30 16:59:19,671  INFO: 测试结束
2025-05-30 16:59:19,672  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-05-30 16:59:19,673  INFO: 测试进度：1/1
2025-05-30 16:59:24,673  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-05-30 16:59:24,676  INFO: 测试完成！总共测试耗时：00:00:05
2025-05-30 16:59:36,197  INFO: 测试开始!
2025-05-30 16:59:36,201  INFO: 测试进度：0/1
2025-05-30 16:59:36,203  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-30 16:59:36,203  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-30 16:59:36,288  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-30 16:59:36,294  INFO: 开始测试
2025-05-30 16:59:36,305  INFO: Generator State: ON
2025-05-30 16:59:36,647  INFO: 测试结束
2025-05-30 16:59:36,648  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-05-30 16:59:36,649  INFO: 测试进度：1/1
2025-05-30 16:59:41,649  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-05-30 16:59:41,652  INFO: 测试完成！总共测试耗时：00:00:05
2025-05-30 16:59:59,562  INFO: 测试开始!
2025-05-30 16:59:59,565  INFO: 测试进度：0/1
2025-05-30 16:59:59,566  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-30 16:59:59,566  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-30 16:59:59,651  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-30 16:59:59,660  INFO: 开始测试
2025-05-30 16:59:59,674  INFO: Generator State: ON
2025-05-30 17:00:13,800  INFO: 测试结束
2025-05-30 17:00:13,803  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-05-30 17:00:13,804  INFO: 测试进度：1/1
2025-05-30 17:00:18,808  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-05-30 17:00:18,811  INFO: 测试完成！总共测试耗时：00:00:19
2025-05-30 17:01:57,398  INFO: 测试开始!
2025-05-30 17:01:57,399  INFO: 测试进度：0/1
2025-05-30 17:01:57,401  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-30 17:01:57,402  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-30 17:01:57,500  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-30 17:01:57,508  INFO: 开始测试
2025-05-30 17:01:57,521  INFO: Generator State: ON
2025-05-30 17:02:24,379  INFO: 测试结束
2025-05-30 17:02:24,383  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-05-30 17:02:24,383  INFO: 测试进度：1/1
2025-05-30 17:02:29,386  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-05-30 17:02:29,388  INFO: 测试完成！总共测试耗时：00:00:32
2025-05-30 17:34:01,142  INFO: 测试开始!
2025-05-30 17:34:01,145  INFO: 测试进度：0/1
2025-05-30 17:34:01,145  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-30 17:34:01,146  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-30 17:34:01,247  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-30 17:34:01,255  INFO: 开始测试
2025-05-30 17:34:01,266  INFO: Generator State: ON
2025-05-30 17:34:23,592  INFO: 测试结束
2025-05-30 17:34:23,595  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-05-30 17:34:23,596  INFO: 测试进度：1/1
2025-05-30 17:34:28,600  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-05-30 17:34:28,604  INFO: 测试完成！总共测试耗时：00:00:27
2025-06-03 10:18:35,457  INFO: 测试开始!
2025-06-03 10:18:35,475  INFO: 测试进度：0/1
2025-06-03 10:18:35,476  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-03 10:18:35,477  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-03 10:18:35,608  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-03 10:18:35,614  ERROR: uart connect error
2025-06-03 10:18:35,617  ERROR: 测试终止！测试错误：uart connect error
2025-06-03 10:18:51,795  INFO: 测试开始!
2025-06-03 10:18:51,796  INFO: 测试进度：0/1
2025-06-03 10:18:51,796  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-03 10:18:51,797  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-03 10:18:51,904  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-03 10:18:51,916  INFO: 开始测试
2025-06-03 10:18:51,928  INFO: Generator State: OFF
2025-06-03 10:18:57,911  INFO: 测试结束
2025-06-03 10:18:57,914  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-06-03 10:18:57,915  INFO: 测试进度：1/1
2025-06-03 10:19:02,920  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-06-03 10:19:02,924  INFO: 测试完成！总共测试耗时：00:00:11
2025-06-03 10:19:27,922  INFO: 测试开始!
2025-06-03 10:19:27,924  INFO: 测试进度：0/1
2025-06-03 10:19:27,925  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-03 10:19:27,926  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-03 10:19:28,019  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-03 10:19:28,029  INFO: 开始测试
2025-06-03 10:19:28,040  INFO: Generator State: PEND
2025-06-03 10:19:33,053  ERROR: 测试终止！测试错误：测试用例执行异常( timed out )
2025-06-03 10:20:02,030  INFO: 测试开始!
2025-06-03 10:20:02,032  INFO: 测试进度：0/1
2025-06-03 10:20:02,033  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-03 10:20:02,037  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-03 10:20:02,119  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-03 10:20:02,129  INFO: 开始测试
2025-06-03 10:20:02,142  INFO: Generator State: ON
2025-06-03 10:20:07,809  INFO: 测试结束
2025-06-03 10:20:07,813  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-06-03 10:20:07,813  INFO: 测试进度：1/1
2025-06-03 10:20:12,815  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-06-03 10:20:12,819  INFO: 测试完成！总共测试耗时：00:00:11
