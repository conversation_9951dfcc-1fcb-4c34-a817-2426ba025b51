2025-06-04 15:11:28,181  INFO: 测试开始!
2025-06-04 15:11:28,184  INFO: 测试进度：0/1
2025-06-04 15:11:28,184  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-04 15:11:28,185  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-04 15:11:28,218  INFO: === 初始化测试环境 ===
2025-06-04 15:11:28,219  INFO: 正在初始化设备连接...
2025-06-04 15:11:28,288  INFO: bluetooth_tester host_id: <PERSON><PERSON><PERSON><PERSON>,CMW,1201.0002k75/101147,3.7.171
2025-06-04 15:11:28,297  INFO: 正在创建测试报告...
2025-06-04 15:11:28,298  ERROR: 测试执行过程中发生异常: too many values to unpack (expected 3)
2025-06-04 15:11:28,298  INFO: === 清理测试环境 ===
2025-06-04 15:11:28,299  INFO: 设备连接已断开
2025-06-04 15:11:28,303  ERROR: 测试终止！测试错误：测试执行过程中发生异常: too many values to unpack (expected 3)
2025-06-04 15:17:42,243  INFO: 测试开始!
2025-06-04 15:17:42,244  INFO: 测试进度：0/1
2025-06-04 15:17:42,245  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-04 15:17:42,245  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-04 15:17:42,269  INFO: === 初始化测试环境 ===
2025-06-04 15:17:42,270  INFO: 正在初始化设备连接...
2025-06-04 15:17:42,339  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-04 15:17:42,344  INFO: 正在创建测试报告...
2025-06-04 15:17:42,344  ERROR: 测试执行过程中发生异常: too many values to unpack (expected 3)
2025-06-04 15:17:42,345  INFO: === 清理测试环境 ===
2025-06-04 15:17:42,346  INFO: 设备连接已断开
2025-06-04 15:17:42,354  ERROR: 测试终止！测试错误：测试执行过程中发生异常: too many values to unpack (expected 3)
2025-06-04 15:19:39,749  INFO: 测试开始!
2025-06-04 15:19:39,752  INFO: 测试进度：0/1
2025-06-04 15:19:39,752  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-04 15:19:39,753  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-04 15:19:39,765  INFO: === 初始化测试环境 ===
2025-06-04 15:19:39,765  INFO: 正在初始化设备连接...
2025-06-04 15:19:39,835  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-04 15:19:39,839  INFO: 正在创建测试报告...
2025-06-04 15:19:39,840  ERROR: 测试执行过程中发生异常: too many values to unpack (expected 3)
2025-06-04 15:19:39,841  INFO: === 清理测试环境 ===
2025-06-04 15:19:39,842  INFO: 设备连接已断开
2025-06-04 15:19:39,849  ERROR: 测试终止！测试错误：测试执行过程中发生异常: too many values to unpack (expected 3)
