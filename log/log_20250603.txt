2025-06-03 10:21:18,003  INFO: 测试开始!
2025-06-03 10:21:18,004  INFO: 测试进度：0/1
2025-06-03 10:21:18,005  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-03 10:21:18,005  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-03 10:21:18,108  INFO: bluetooth_tester host_id: <PERSON><PERSON><PERSON><PERSON>,CMW,1201.0002k75/101147,3.7.171
2025-06-03 10:21:18,119  INFO: 开始测试
2025-06-03 10:21:18,131  INFO: Generator State: ON
2025-06-03 10:21:23,798  INFO: 测试结束
2025-06-03 10:21:23,802  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-06-03 10:21:23,802  INFO: 测试进度：1/1
2025-06-03 10:21:28,807  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-06-03 10:21:28,812  INFO: 测试完成！总共测试耗时：00:00:11
2025-06-03 10:22:36,800  INFO: 测试开始!
2025-06-03 10:22:36,803  INFO: 测试进度：0/1
2025-06-03 10:22:36,803  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-03 10:22:36,804  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-03 10:22:36,902  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-03 10:22:36,916  INFO: 开始测试
2025-06-03 10:22:36,929  INFO: Generator State: ON
2025-06-03 10:22:42,670  INFO: 测试结束
2025-06-03 10:22:42,673  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-06-03 10:22:42,673  INFO: 测试进度：1/1
2025-06-03 10:22:47,677  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-06-03 10:22:47,682  INFO: 测试完成！总共测试耗时：00:00:11
2025-06-03 10:28:01,891  INFO: 测试开始!
2025-06-03 10:28:01,894  INFO: 测试进度：0/1
2025-06-03 10:28:01,894  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-03 10:28:01,895  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-03 10:28:01,983  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-03 10:28:01,993  INFO: 开始测试
2025-06-03 10:28:02,004  INFO: Generator State: ON
2025-06-03 10:28:35,612  INFO: 测试结束
2025-06-03 10:28:35,616  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-06-03 10:28:35,616  INFO: 测试进度：1/1
2025-06-03 10:28:40,621  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-06-03 10:28:40,624  INFO: 测试完成！总共测试耗时：00:00:39
2025-06-03 13:21:27,026  INFO: 测试开始!
2025-06-03 13:21:27,028  INFO: 测试进度：0/1
2025-06-03 13:21:27,029  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-03 13:21:27,029  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-03 13:21:27,120  ERROR: 测试终止！测试错误：测试用例执行异常( cannot import name 'first' from 'zmq' (/Users/<USER>/.virtualenvs/gxbtt_env/lib/python3.8/site-packages/zmq/__init__.py) )
2025-06-03 13:22:33,951  INFO: 测试开始!
2025-06-03 13:22:33,953  INFO: 测试进度：0/1
2025-06-03 13:22:33,954  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-03 13:22:33,955  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-03 13:22:34,053  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-03 13:22:34,062  INFO: 开始测试
2025-06-03 13:22:34,073  INFO: Generator State: ON
2025-06-03 13:22:36,172  INFO: level: INV
2025-06-03 13:22:36,372  ERROR: 测试终止！测试错误：测试用例执行异常( 'CmwFunc' object has no attribute 'cmw_func_gprf_generator_create_arb_file_path_from_params' )
2025-06-03 13:26:33,275  INFO: 测试开始!
2025-06-03 13:26:33,278  INFO: 测试进度：0/1
2025-06-03 13:26:33,278  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-03 13:26:33,279  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-03 13:26:33,346  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-03 13:26:33,357  INFO: 开始测试
2025-06-03 13:26:33,368  INFO: Generator State: OFF
2025-06-03 13:26:35,429  INFO: level: INV
2025-06-03 13:26:35,641  ERROR: 测试终止！测试错误：测试用例执行异常( name 'sagitta_symbol_rate' is not defined )
2025-06-03 13:31:00,281  INFO: 测试开始!
2025-06-03 13:31:00,294  INFO: 测试进度：0/1
2025-06-03 13:31:00,295  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-03 13:31:00,295  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-03 13:31:00,357  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-03 13:31:00,365  INFO: 开始测试
2025-06-03 13:31:00,376  INFO: Generator State: OFF
2025-06-03 13:31:02,401  INFO: level: INV
2025-06-03 13:31:02,602  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-06-03 13:31:02,603  INFO: 测试进度：1/1
2025-06-03 13:31:07,607  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-06-03 13:31:07,613  INFO: 测试完成！总共测试耗时：00:00:07
2025-06-03 13:32:21,646  INFO: 测试开始!
2025-06-03 13:32:21,651  INFO: 测试进度：0/1
2025-06-03 13:32:21,653  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-03 13:32:21,654  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-03 13:32:21,721  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-03 13:32:21,730  INFO: 开始测试
2025-06-03 13:32:21,745  INFO: Generator State: OFF
2025-06-03 13:32:23,949  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-06-03 13:32:23,951  INFO: 测试进度：1/1
2025-06-03 13:32:28,954  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-06-03 13:32:28,957  INFO: 测试完成！总共测试耗时：00:00:07
2025-06-03 13:33:10,128  INFO: 测试开始!
2025-06-03 13:33:10,129  INFO: 测试进度：0/1
2025-06-03 13:33:10,131  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-03 13:33:10,132  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-03 13:33:10,194  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-03 13:33:10,201  INFO: 开始测试
2025-06-03 13:33:10,213  INFO: Generator State: OFF
2025-06-03 13:33:12,418  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-06-03 13:33:12,420  INFO: 测试进度：1/1
2025-06-03 13:33:17,420  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-06-03 13:33:17,424  INFO: 测试完成！总共测试耗时：00:00:07
2025-06-03 13:34:19,472  INFO: 测试开始!
2025-06-03 13:34:19,473  INFO: 测试进度：0/1
2025-06-03 13:34:19,473  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-03 13:34:19,474  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-03 13:34:19,541  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-03 13:34:19,556  INFO: 开始测试
2025-06-03 13:34:19,568  INFO: Generator State: OFF
2025-06-03 13:34:21,976  INFO: file: "No File Selected"
2025-06-03 13:34:34,969  INFO: 测试停止!
2025-06-03 13:43:34,497  INFO: 测试开始!
2025-06-03 13:43:34,498  INFO: 测试进度：0/1
2025-06-03 13:43:34,499  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-03 13:43:34,499  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-03 13:43:34,599  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-03 13:43:34,609  INFO: 开始测试
2025-06-03 13:43:34,621  INFO: Generator State: OFF
2025-06-03 13:43:37,043  INFO: file: "No File Selected"
2025-06-03 13:43:58,247  INFO: 测试停止!
2025-06-03 13:44:46,251  INFO: 测试开始!
2025-06-03 13:44:46,252  INFO: 测试进度：0/1
2025-06-03 13:44:46,254  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-03 13:44:46,254  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-03 13:44:46,343  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-03 13:44:46,351  INFO: 开始测试
2025-06-03 13:44:46,363  INFO: Generator State: OFF
2025-06-03 13:44:48,773  INFO: file: "No File Selected"
2025-06-03 13:45:16,829  INFO: 测试停止!
2025-06-03 13:49:03,431  INFO: 测试开始!
2025-06-03 13:49:03,439  INFO: 测试进度：0/1
2025-06-03 13:49:03,439  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-03 13:49:03,444  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-03 13:49:03,525  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-03 13:49:03,534  INFO: 开始测试
2025-06-03 13:49:03,545  INFO: Generator State: OFF
2025-06-03 13:49:05,965  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar4_8\qpsk_1Rs_24Fs_1000fps_PType2_seed0_4bk_sym2db_grdc.wv"
2025-06-03 13:50:49,568  INFO: 测试结束
2025-06-03 13:50:49,571  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-06-03 13:50:49,572  INFO: 测试进度：1/1
2025-06-03 13:50:54,571  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-06-03 13:50:54,574  INFO: 测试完成！总共测试耗时：00:01:51
2025-06-03 13:53:24,334  INFO: 测试开始!
2025-06-03 13:53:24,336  INFO: 测试进度：0/1
2025-06-03 13:53:24,337  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-03 13:53:24,337  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-03 13:53:24,425  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-03 13:53:24,434  INFO: 开始测试
2025-06-03 13:53:24,447  INFO: Generator State: ON
2025-06-03 13:53:26,960  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar4_8\qpsk_1Rs_24Fs_1000fps_PType2_seed0_4bk_sym1db_grdc.wv"
2025-06-03 13:56:53,068  INFO: 测试结束
2025-06-03 13:56:53,072  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-06-03 13:56:53,074  INFO: 测试进度：1/1
2025-06-03 13:56:58,077  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-06-03 13:56:58,081  INFO: 测试完成！总共测试耗时：00:02:32
2025-06-03 13:57:40,543  INFO: 测试开始!
2025-06-03 13:57:40,545  INFO: 测试进度：0/1
2025-06-03 13:57:40,545  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-03 13:57:40,546  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-03 13:57:40,625  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-03 13:57:40,633  INFO: 开始测试
2025-06-03 13:57:40,644  INFO: Generator State: ON
2025-06-03 13:57:43,076  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar4_8\qpsk_1Rs_24Fs_1000fps_PType2_seed0_4bk_sym1db_grdc.wv"
2025-06-03 13:58:42,915  INFO: 测试结束
2025-06-03 13:58:42,919  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-06-03 13:58:42,920  INFO: 测试进度：1/1
2025-06-03 13:58:47,920  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-06-03 13:58:47,925  INFO: 测试完成！总共测试耗时：00:01:08
2025-06-03 14:15:38,657  INFO: 测试开始!
2025-06-03 14:15:38,659  INFO: 测试进度：0/1
2025-06-03 14:15:38,659  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-03 14:15:38,660  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-03 14:15:38,761  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-03 14:15:38,770  INFO: 开始测试
2025-06-03 14:15:38,782  INFO: 测试结束
2025-06-03 14:15:38,786  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-06-03 14:15:38,786  INFO: 测试进度：1/1
2025-06-03 14:15:43,788  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-06-03 14:15:43,792  INFO: 测试完成！总共测试耗时：00:00:05
2025-06-03 14:16:07,897  INFO: 测试开始!
2025-06-03 14:16:07,898  INFO: 测试进度：0/1
2025-06-03 14:16:07,899  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-03 14:16:07,899  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-03 14:16:07,988  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-03 14:16:07,997  INFO: 开始测试
2025-06-03 14:16:08,010  INFO: 测试结束
2025-06-03 14:16:08,012  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-06-03 14:16:08,013  INFO: 测试进度：1/1
2025-06-03 14:16:13,015  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-06-03 14:16:13,018  INFO: 测试完成！总共测试耗时：00:00:05
2025-06-03 14:24:49,336  INFO: 测试开始!
2025-06-03 14:24:49,338  INFO: 测试进度：0/1
2025-06-03 14:24:49,338  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-03 14:24:49,339  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-03 14:24:49,436  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-03 14:24:49,445  INFO: 开始测试
2025-06-03 14:24:49,452  ERROR: 测试终止！测试错误：测试用例执行异常( cmw_func_query_gprf_generator_arb_file_is_exist() got an unexpected keyword argument 'specify_dir' )
2025-06-03 14:25:37,683  INFO: 测试开始!
2025-06-03 14:25:37,684  INFO: 测试进度：0/1
2025-06-03 14:25:37,685  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-03 14:25:37,685  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-03 14:25:37,775  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-03 14:25:37,784  INFO: 开始测试
2025-06-03 14:25:37,795  INFO: 测试结束
2025-06-03 14:25:37,797  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-06-03 14:25:37,797  INFO: 测试进度：1/1
2025-06-03 14:25:42,801  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-06-03 14:25:42,804  INFO: 测试完成！总共测试耗时：00:00:05
2025-06-03 14:26:32,865  INFO: 测试开始!
2025-06-03 14:26:32,870  INFO: 测试进度：0/1
2025-06-03 14:26:32,870  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-03 14:26:32,870  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-03 14:26:32,957  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-03 14:26:32,966  INFO: 开始测试
2025-06-03 14:26:32,978  INFO: 测试结束
2025-06-03 14:26:32,983  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-06-03 14:26:32,983  INFO: 测试进度：1/1
2025-06-03 14:26:37,988  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-06-03 14:26:37,991  INFO: 测试完成！总共测试耗时：00:00:05
2025-06-03 14:27:28,331  INFO: 测试开始!
2025-06-03 14:27:28,333  INFO: 测试进度：0/1
2025-06-03 14:27:28,334  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-03 14:27:28,335  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-03 14:27:28,419  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-03 14:27:28,428  INFO: 开始测试
2025-06-03 14:27:28,441  INFO: 测试结束
2025-06-03 14:27:28,447  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-06-03 14:27:28,447  INFO: 测试进度：1/1
2025-06-03 14:27:33,453  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-06-03 14:27:33,457  INFO: 测试完成！总共测试耗时：00:00:05
2025-06-03 14:30:55,868  INFO: 测试开始!
2025-06-03 14:30:55,869  INFO: 测试进度：0/1
2025-06-03 14:30:55,872  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-03 14:30:55,872  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-03 14:30:55,963  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-03 14:30:55,972  INFO: 开始测试
2025-06-03 14:30:55,983  INFO: 测试结束
2025-06-03 14:30:55,986  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-06-03 14:30:55,986  INFO: 测试进度：1/1
2025-06-03 14:31:00,990  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-06-03 14:31:00,994  INFO: 测试完成！总共测试耗时：00:00:05
2025-06-03 14:35:34,757  INFO: 测试开始!
2025-06-03 14:35:34,758  INFO: 测试进度：0/1
2025-06-03 14:35:34,760  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-03 14:35:34,761  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-03 14:35:34,851  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-03 14:35:34,861  INFO: 开始测试
2025-06-03 14:35:34,873  INFO: 测试结束
2025-06-03 14:35:34,875  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-06-03 14:35:34,876  INFO: 测试进度：1/1
2025-06-03 14:35:39,876  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-06-03 14:35:39,879  INFO: 测试完成！总共测试耗时：00:00:05
2025-06-03 15:30:55,423  INFO: 测试开始!
2025-06-03 15:30:55,426  INFO: 测试进度：0/1
2025-06-03 15:30:55,428  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-03 15:30:55,431  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-03 15:30:55,529  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-03 15:30:55,538  INFO: 开始测试
2025-06-03 15:30:55,542  ERROR: 测试终止！测试错误：测试用例执行异常( name 'sagitta_signal_channel_chg' is not defined )
2025-06-03 15:58:35,305  INFO: 测试开始!
2025-06-03 15:58:35,307  INFO: 测试进度：0/1
2025-06-03 15:58:35,311  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-03 15:58:35,313  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-03 15:58:35,413  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-03 15:58:35,422  INFO: 开始测试
2025-06-03 15:58:35,427  ERROR: 测试终止！测试错误：测试用例执行异常( name 'sagitta_signal_channel_chg' is not defined )
2025-06-03 15:59:15,073  INFO: 测试开始!
2025-06-03 15:59:15,076  INFO: 测试进度：0/1
2025-06-03 15:59:15,077  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-03 15:59:15,077  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-03 15:59:15,096  ERROR: 测试终止！测试错误：测试用例执行异常( cannot import name 'sagitta_symbol_rate_chg' from 'module_set.test_params' (/Users/<USER>/workspace/bluetooth_test/module_set/test_params.py) )
2025-06-03 16:00:12,849  INFO: 测试开始!
2025-06-03 16:00:12,852  INFO: 测试进度：0/1
2025-06-03 16:00:12,853  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-03 16:00:12,853  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-03 16:00:12,943  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-03 16:00:12,953  INFO: 开始测试
2025-06-03 16:00:12,965  INFO: Generator State: ON
2025-06-03 16:00:14,211  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-03 16:26:44,885  INFO: 测试停止!
2025-06-03 16:31:38,001  INFO: 测试开始!
2025-06-03 16:31:38,008  INFO: 测试进度：0/1
2025-06-03 16:31:38,013  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-03 16:31:38,013  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-03 16:31:38,122  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-03 16:31:38,131  INFO: 开始测试
2025-06-03 16:31:38,145  INFO: Generator State: ON
2025-06-03 16:31:39,371  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-03 16:31:45,696  INFO: rx_ok is 0
2025-06-03 16:31:46,122  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-03 16:31:52,440  INFO: rx_ok is 0
2025-06-03 16:31:52,872  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-03 16:31:59,192  INFO: rx_ok is 0
2025-06-03 16:31:59,622  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-03 16:32:05,915  INFO: rx_ok is 0
2025-06-03 16:32:06,350  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-03 16:32:12,687  INFO: rx_ok is 0
2025-06-03 16:32:12,687  INFO: 测试结束
2025-06-03 16:32:12,690  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-06-03 16:32:12,690  INFO: 测试进度：1/1
2025-06-03 16:32:17,692  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-06-03 16:32:17,696  INFO: 测试完成！总共测试耗时：00:00:39
2025-06-03 16:36:54,040  INFO: 测试开始!
2025-06-03 16:36:54,041  INFO: 测试进度：0/1
2025-06-03 16:36:54,042  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-03 16:36:54,043  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-03 16:36:54,133  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-03 16:36:54,142  INFO: 开始测试
2025-06-03 16:36:54,154  INFO: Generator State: ON
2025-06-03 16:36:55,374  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-03 16:37:01,673  INFO: rx_ok is 0
2025-06-03 16:37:06,903  ERROR: 测试终止！测试错误：测试用例执行异常( timed out )
2025-06-03 16:40:39,347  INFO: 测试开始!
2025-06-03 16:40:39,349  INFO: 测试进度：0/1
2025-06-03 16:40:39,350  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-03 16:40:39,350  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-03 16:40:39,442  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-03 16:40:39,455  INFO: 开始测试
2025-06-03 16:40:39,467  INFO: Generator State: ON
2025-06-03 16:40:40,718  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-03 16:41:57,242  INFO: rx_ok is 0
2025-06-03 16:41:57,256  INFO: Generator State: ON
2025-06-03 16:41:58,496  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym1db_grdc.wv"
2025-06-03 16:43:15,049  INFO: rx_ok is 0
2025-06-03 16:43:15,060  INFO: Generator State: ON
2025-06-03 16:43:16,388  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym2db_grdc.wv"
2025-06-03 16:44:37,077  INFO: 测试停止!
2025-06-03 16:44:45,438  INFO: 测试开始!
2025-06-03 16:44:45,439  INFO: 测试进度：0/1
2025-06-03 16:44:45,440  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-03 16:44:45,440  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-03 16:44:45,529  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-03 16:44:45,537  INFO: 开始测试
2025-06-03 16:44:45,549  INFO: Generator State: ON
2025-06-03 16:44:46,771  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-03 16:46:51,420  INFO: {'per': 0.3233374492855883, 'first_data': {'rx_ok': 12642, 'tx': 0, 'crc_err': 2401, 'len_err': 0, 'sync_err': 4603}, 'second_data': {'rx_ok': 31822, 'tx': 0, 'crc_err': 4701, 'len_err': 0, 'sync_err': 11468}, 'register_data_history': [{'timestamp': '2025-06-03 16:46:22.097', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '3.2959e-02'}, {'timestamp': '2025-06-03 16:46:24.456', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '1.7944e-02'}, {'timestamp': '2025-06-03 16:46:26.813', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '1.0620e-02'}, {'timestamp': '2025-06-03 16:46:29.186', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '1.2207e-02'}, {'timestamp': '2025-06-03 16:46:31.555', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '1.7944e-02'}, {'timestamp': '2025-06-03 16:46:33.921', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '1.6357e-02'}, {'timestamp': '2025-06-03 16:46:36.298', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '4.4067e-02'}, {'timestamp': '2025-06-03 16:46:38.688', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '1.3428e-02'}, {'timestamp': '2025-06-03 16:46:41.060', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '1.8555e-02'}, {'timestamp': '2025-06-03 16:46:43.442', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '9.5215e-03'}, {'timestamp': '2025-06-03 16:46:45.814', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '3.0273e-02'}, {'timestamp': '2025-06-03 16:46:48.180', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '1.0254e-02'}, {'timestamp': '2025-06-03 16:46:50.568', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '1.2329e-02'}]}
2025-06-03 16:46:51,431  INFO: Generator State: ON
2025-06-03 16:46:52,654  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym1db_grdc.wv"
2025-06-03 16:48:36,154  INFO: {'per': 0.12317624273724981, 'first_data': {'rx_ok': 12615, 'tx': 0, 'crc_err': 1964, 'len_err': 0, 'sync_err': 1433}, 'second_data': {'rx_ok': 26197, 'tx': 0, 'crc_err': 2347, 'len_err': 0, 'sync_err': 2958}, 'register_data_history': [{'timestamp': '2025-06-03 16:48:22.465', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '4.7607e-03'}, {'timestamp': '2025-06-03 16:48:24.825', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '3.6621e-03'}, {'timestamp': '2025-06-03 16:48:27.186', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '8.7891e-03'}, {'timestamp': '2025-06-03 16:48:29.553', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '9.7656e-03'}, {'timestamp': '2025-06-03 16:48:31.923', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '3.0518e-03'}, {'timestamp': '2025-06-03 16:48:34.293', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '2.1973e-03'}]}
2025-06-03 16:48:36,165  INFO: Generator State: ON
2025-06-03 16:48:37,381  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym2db_grdc.wv"
2025-06-03 16:50:20,902  INFO: {'per': 0.033952390069564076, 'first_data': {'rx_ok': 14620, 'tx': 0, 'crc_err': 1354, 'len_err': 0, 'sync_err': 344}, 'second_data': {'rx_ok': 29757, 'tx': 0, 'crc_err': 1528, 'len_err': 0, 'sync_err': 702}, 'register_data_history': [{'timestamp': '2025-06-03 16:50:07.192', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '3.7842e-03'}, {'timestamp': '2025-06-03 16:50:09.560', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '2.5635e-03'}, {'timestamp': '2025-06-03 16:50:11.924', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '1.4648e-03'}, {'timestamp': '2025-06-03 16:50:14.277', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '1.0986e-03'}, {'timestamp': '2025-06-03 16:50:16.650', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '1.2207e-03'}, {'timestamp': '2025-06-03 16:50:19.039', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '1.0986e-03'}]}
2025-06-03 16:50:20,913  INFO: Generator State: ON
2025-06-03 16:50:22,276  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym3db_grdc.wv"
2025-06-03 16:52:05,793  INFO: {'per': 0.004534423297994583, 'first_data': {'rx_ok': 15198, 'tx': 0, 'crc_err': 1136, 'len_err': 0, 'sync_err': 50}, 'second_data': {'rx_ok': 30785, 'tx': 0, 'crc_err': 1164, 'len_err': 0, 'sync_err': 93}, 'register_data_history': [{'timestamp': '2025-06-03 16:51:52.095', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '0.0000e+00'}, {'timestamp': '2025-06-03 16:51:54.459', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '6.1035e-04'}, {'timestamp': '2025-06-03 16:51:56.840', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '9.7656e-04'}, {'timestamp': '2025-06-03 16:51:59.208', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '6.1035e-04'}, {'timestamp': '2025-06-03 16:52:01.553', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '0.0000e+00'}, {'timestamp': '2025-06-03 16:52:03.927', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '1.2207e-04'}]}
2025-06-03 16:52:05,803  INFO: Generator State: ON
2025-06-03 16:52:07,037  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym4db_grdc.wv"
2025-06-03 16:53:45,921  INFO: {'per': 0.0001856895271106218, 'first_data': {'rx_ok': 10226, 'tx': 0, 'crc_err': 967, 'len_err': 0, 'sync_err': 0}, 'second_data': {'rx_ok': 26379, 'tx': 0, 'crc_err': 970, 'len_err': 0, 'sync_err': 0}, 'register_data_history': [{'timestamp': '2025-06-03 16:53:31.838', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '0.0000e+00'}, {'timestamp': '2025-06-03 16:53:34.268', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '0.0000e+00'}, {'timestamp': '2025-06-03 16:53:36.741', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '0.0000e+00'}, {'timestamp': '2025-06-03 16:53:39.185', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '0.0000e+00'}, {'timestamp': '2025-06-03 16:53:41.617', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '0.0000e+00'}, {'timestamp': '2025-06-03 16:53:44.075', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '1.2207e-04'}]}
2025-06-03 16:53:45,925  INFO: 测试结束
2025-06-03 16:53:45,927  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-06-03 16:53:45,927  INFO: 测试进度：1/1
2025-06-03 16:53:50,929  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-06-03 16:53:50,932  INFO: 测试完成！总共测试耗时：00:09:04
2025-06-03 18:52:50,606  INFO: 测试开始!
2025-06-03 18:52:50,608  INFO: 测试进度：0/1
2025-06-03 18:52:50,609  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-03 18:52:50,609  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-03 18:52:50,717  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-03 18:52:50,727  INFO: 开始测试
2025-06-03 18:52:50,727  ERROR: cur_tc: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 1, 'signal_ch': 0}
2025-06-03 18:52:50,738  INFO: Generator State: ON
2025-06-03 18:52:51,952  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-03 18:54:56,536  ERROR: 测试终止！测试错误：测试用例执行异常( string indices must be integers )
2025-06-03 19:16:35,386  INFO: 测试开始!
2025-06-03 19:16:35,389  INFO: 测试进度：0/1
2025-06-03 19:16:35,390  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-03 19:16:35,390  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-03 19:16:35,490  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-03 19:16:35,500  INFO: 开始测试
2025-06-03 19:16:35,500  ERROR: cur_tc: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 1, 'signal_ch': 0}
2025-06-03 19:16:35,511  INFO: Generator State: ON
2025-06-03 19:16:36,747  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-03 19:17:31,191  ERROR: cur_tc: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 2, 'signal_ch': 0}
2025-06-03 19:17:31,205  INFO: Generator State: ON
2025-06-03 19:17:32,445  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym1db_grdc.wv"
2025-06-03 19:18:28,823  INFO: 测试停止!
2025-06-03 19:19:27,734  INFO: 测试开始!
2025-06-03 19:19:27,735  INFO: 测试进度：0/1
2025-06-03 19:19:27,737  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-03 19:19:27,738  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-03 19:19:27,835  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-03 19:19:27,844  INFO: 开始测试
2025-06-03 19:19:27,844  ERROR: cur_tc: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 1, 'signal_ch': 0}
2025-06-03 19:19:27,857  INFO: Generator State: ON
2025-06-03 19:19:29,080  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-03 19:21:33,843  ERROR: cur_tc: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 2, 'signal_ch': 0}
2025-06-03 19:21:33,857  INFO: Generator State: ON
2025-06-03 19:21:35,078  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym1db_grdc.wv"
2025-06-03 19:22:00,153  INFO: 测试停止!
2025-06-03 19:22:35,513  INFO: 测试开始!
2025-06-03 19:22:35,515  INFO: 测试进度：0/1
2025-06-03 19:22:35,516  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-03 19:22:35,517  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-03 19:22:35,671  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-03 19:22:35,680  INFO: 开始测试
2025-06-03 19:22:35,681  ERROR: cur_tc: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 1, 'signal_ch': 0}
2025-06-03 19:22:35,693  INFO: Generator State: PEND
2025-06-03 19:22:40,710  ERROR: 测试终止！测试错误：测试用例执行异常( timed out )
2025-06-03 19:28:45,717  INFO: 测试开始!
2025-06-03 19:28:45,724  INFO: 测试进度：0/1
2025-06-03 19:28:45,725  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-03 19:28:45,725  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-03 19:28:45,832  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-03 19:28:45,841  INFO: 开始测试
2025-06-03 19:28:45,842  ERROR: cur_tc: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 1, 'signal_ch': 0}
2025-06-03 19:28:52,096  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-03 19:30:56,854  INFO: result: per: 0.32083026024654937, polar_err: 8.5449e-03~4.3945e-02
2025-06-03 19:30:56,855  ERROR: cur_tc: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 2, 'signal_ch': 0}
2025-06-03 19:31:03,100  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym1db_grdc.wv"
2025-06-03 19:32:44,137  INFO: result: per: 0.12554028772337267, polar_err: 2.8076e-03~1.3550e-02
2025-06-03 19:32:44,137  ERROR: cur_tc: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 3, 'signal_ch': 0}
2025-06-03 19:32:50,388  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym2db_grdc.wv"
2025-06-03 19:34:35,580  INFO: result: per: 0.033123121353215046, polar_err: 7.3242e-04~4.6387e-03
2025-06-03 19:34:35,582  ERROR: cur_tc: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 4, 'signal_ch': 0}
2025-06-03 19:35:02,999  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym3db_grdc.wv"
2025-06-03 19:36:43,143  INFO: result: per: 0.004532686414708942, polar_err: 0.0000e+00~7.3242e-04
2025-06-03 19:36:43,145  ERROR: cur_tc: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 5, 'signal_ch': 0}
2025-06-03 19:36:49,399  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym4db_grdc.wv"
2025-06-03 19:38:32,937  INFO: result: per: 0.0002542265158256285, polar_err: 0.0000e+00~2.4414e-04
2025-06-03 19:38:32,937  ERROR: cur_tc: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 6, 'signal_ch': 0}
2025-06-03 19:38:39,196  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym5db_grdc.wv"
2025-06-03 19:40:17,280  INFO: result: per: 6.36618283677004e-05, polar_err: 0.0000e+00~1.2207e-04
2025-06-03 19:40:17,281  ERROR: cur_tc: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 3, 'snr': 1, 'signal_ch': 0}
2025-06-03 19:40:23,325  ERROR: ARB 文件不存在
2025-06-03 19:40:23,325  ERROR: cur_tc: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 3, 'snr': 2, 'signal_ch': 0}
2025-06-03 19:40:24,650  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar3_8\qpsk_1Rs_24Fs_1000fps_PType1_seed0_5bk_sym1db_grdc.wv"
2025-06-03 19:42:08,119  INFO: result: per: 0.19825509473272995, polar_err: 1.9897e-02~3.5522e-02
2025-06-03 19:42:08,120  ERROR: cur_tc: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 3, 'snr': 3, 'signal_ch': 0}
2025-06-03 19:42:14,359  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar3_8\qpsk_1Rs_24Fs_1000fps_PType1_seed0_5bk_sym2db_grdc.wv"
2025-06-03 19:44:24,949  ERROR: cur_tc: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 3, 'snr': 4, 'signal_ch': 0}
2025-06-03 19:44:31,346  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar3_8\qpsk_1Rs_24Fs_1000fps_PType1_seed0_5bk_sym3db_grdc.wv"
2025-06-03 19:47:23,909  INFO: 测试结束
2025-06-03 19:47:23,913  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-06-03 19:47:23,915  INFO: 测试进度：1/1
2025-06-03 19:47:28,915  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-06-03 19:47:28,918  INFO: 测试完成！总共测试耗时：00:15:31
2025-06-03 19:49:12,855  INFO: 测试开始!
2025-06-03 19:49:12,859  INFO: 测试进度：0/1
2025-06-03 19:49:12,859  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-03 19:49:12,860  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-03 19:49:12,953  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-03 19:49:12,962  INFO: 开始测试
2025-06-03 19:49:12,963  ERROR: cur_tc: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 1, 'signal_ch': 0}
2025-06-03 19:49:19,217  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-03 19:50:35,031  ERROR: cur_tc: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 2, 'signal_ch': 0}
2025-06-03 19:50:41,285  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym1db_grdc.wv"
2025-06-03 19:52:02,854  ERROR: cur_tc: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 3, 'signal_ch': 0}
2025-06-03 19:52:09,100  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym2db_grdc.wv"
2025-06-03 19:52:16,091  INFO: 测试停止!
2025-06-03 19:55:09,006  INFO: 测试开始!
2025-06-03 19:55:09,015  INFO: 测试进度：0/1
2025-06-03 19:55:09,015  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-03 19:55:09,016  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-03 19:55:09,105  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-03 19:55:09,115  INFO: 开始测试
2025-06-03 19:55:09,116  ERROR: cur_tc: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 1, 'signal_ch': 0}
2025-06-03 19:55:15,428  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-03 19:57:06,943  INFO: result: per: 0.310175302412834, polar_err: 8.7891e-03~2.4780e-02
2025-06-03 19:57:06,947  ERROR: cur_tc: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 2, 'signal_ch': 0}
2025-06-03 19:58:14,546  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym1db_grdc.wv"
2025-06-03 20:00:52,877  INFO: result: per: 0.1282265851303508, polar_err: 1.0986e-03~1.6724e-02
2025-06-03 20:00:52,879  ERROR: cur_tc: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 3, 'signal_ch': 0}
2025-06-03 20:00:59,126  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym2db_grdc.wv"
2025-06-03 20:02:42,663  INFO: result: per: 0.03403007902115729, polar_err: 2.4414e-04~8.0566e-03
2025-06-03 20:02:42,666  ERROR: cur_tc: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 4, 'signal_ch': 0}
2025-06-03 20:02:48,909  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym3db_grdc.wv"
2025-06-03 20:04:32,415  INFO: result: per: 0.004403318442884441, polar_err: 0.0000e+00~1.0986e-03
2025-06-03 20:04:32,415  ERROR: cur_tc: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 5, 'signal_ch': 0}
2025-06-03 20:04:38,717  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym4db_grdc.wv"
2025-06-03 20:06:22,256  INFO: result: per: 0.0001277873618299452, polar_err: 0.0000e+00~1.2207e-04
2025-06-03 20:06:22,256  ERROR: cur_tc: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 6, 'signal_ch': 0}
2025-06-03 20:06:28,514  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym5db_grdc.wv"
2025-06-03 20:08:06,635  INFO: result: per: 0.0, polar_err: 0.0000e+00~0.0000e+00
2025-06-03 20:08:06,636  ERROR: cur_tc: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 3, 'snr': 1, 'signal_ch': 0}
2025-06-03 20:08:12,679  ERROR: ARB 文件不存在
2025-06-03 20:08:12,680  ERROR: cur_tc: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 3, 'snr': 2, 'signal_ch': 0}
2025-06-03 20:08:13,920  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar3_8\qpsk_1Rs_24Fs_1000fps_PType1_seed0_5bk_sym1db_grdc.wv"
2025-06-03 20:09:57,445  INFO: result: per: 0.20010398388249817, polar_err: 2.0874e-02~2.9785e-02
2025-06-03 20:09:57,445  ERROR: cur_tc: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 3, 'snr': 3, 'signal_ch': 0}
2025-06-03 20:10:03,698  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar3_8\qpsk_1Rs_24Fs_1000fps_PType1_seed0_5bk_sym2db_grdc.wv"
2025-06-03 20:11:47,231  INFO: result: per: 0.046092056610990695, polar_err: 2.8076e-03~7.2021e-03
2025-06-03 20:11:47,231  ERROR: cur_tc: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 3, 'snr': 4, 'signal_ch': 0}
2025-06-03 20:11:53,475  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar3_8\qpsk_1Rs_24Fs_1000fps_PType1_seed0_5bk_sym3db_grdc.wv"
2025-06-03 20:13:37,000  INFO: result: per: 0.008943972401456612, polar_err: 4.8828e-04~2.5635e-03
2025-06-03 20:13:37,001  INFO: 测试结束
2025-06-03 20:13:37,004  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-06-03 20:13:37,004  INFO: 测试进度：1/1
2025-06-03 20:13:42,008  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-06-03 20:13:42,013  INFO: 测试完成！总共测试耗时：00:15:38
2025-06-04 09:39:50,747  INFO: 测试开始!
2025-06-04 09:39:50,756  INFO: 测试进度：0/1
2025-06-04 09:39:50,756  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-04 09:39:50,758  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-04 09:39:55,800  ERROR: connect bluetooth_tester timeout
2025-06-04 09:39:55,803  ERROR: 测试终止！测试错误：connect bluetooth_tester timeout
2025-06-04 09:41:22,587  INFO: 测试开始!
2025-06-04 09:41:22,588  INFO: 测试进度：0/1
2025-06-04 09:41:22,588  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-04 09:41:22,589  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-04 09:41:22,671  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-04 09:41:22,687  INFO: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告
2025-06-04 09:41:22,687  INFO: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250604_094122
2025-06-04 09:41:22,688  INFO: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250604_094122/superlink_rx_gauss_report_20250604_094122.xlsx
2025-06-04 09:41:22,689  INFO: 开始测试
2025-06-04 09:41:22,689  ERROR: cur_tc: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 1, 'signal_ch': 0}
2025-06-04 09:41:23,987  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-04 09:47:19,761  ERROR: cur_tc: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 2, 'signal_ch': 0}
2025-06-04 09:47:26,042  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym1db_grdc.wv"
2025-06-04 09:48:47,554  INFO: 测试停止!
2025-06-04 09:53:07,272  INFO: 测试开始!
2025-06-04 09:53:07,278  INFO: 测试进度：0/1
2025-06-04 09:53:07,279  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-04 09:53:07,279  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-04 09:53:07,392  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-04 09:53:07,403  INFO: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告
2025-06-04 09:53:07,405  INFO: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250604_095307
2025-06-04 09:53:07,406  INFO: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250604_095307/superlink_rx_gauss_report_20250604_095307.xlsx
2025-06-04 09:53:07,407  INFO: 开始测试
2025-06-04 09:53:07,407  ERROR: cur_tc: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 1, 'signal_ch': 0}
2025-06-04 09:53:08,663  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-04 09:55:29,036  ERROR: cur_tc: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 2, 'signal_ch': 0}
2025-06-04 09:55:35,340  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym1db_grdc.wv"
2025-06-04 09:55:39,479  INFO: 测试停止!
2025-06-04 09:57:10,140  INFO: 测试开始!
2025-06-04 09:57:10,141  INFO: 测试进度：0/1
2025-06-04 09:57:10,142  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-04 09:57:10,142  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-04 09:57:10,225  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-04 09:57:10,235  INFO: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告
2025-06-04 09:57:10,236  INFO: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250604_095710
2025-06-04 09:57:10,237  INFO: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250604_095710/superlink_rx_gauss_report_20250604_095710.xlsx
2025-06-04 09:57:10,237  INFO: 开始测试
2025-06-04 09:57:10,238  ERROR: cur_tc: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 1, 'signal_ch': 0}
2025-06-04 09:57:16,537  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-04 09:59:21,553  INFO: result: per: 0.3204088083447476, polar_err: 8.4229e-03~4.1870e-02
2025-06-04 09:59:21,627  ERROR: 测试终止！测试错误：测试用例执行异常( Cannot convert {'rx_ok': 12536, 'tx': 0, 'crc_err': 2460, 'len_err': 0, 'sync_err': 4539} to Excel )
2025-06-04 10:03:55,641  INFO: 测试开始!
2025-06-04 10:03:55,642  INFO: 测试进度：0/1
2025-06-04 10:03:55,643  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-04 10:03:55,643  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-04 10:03:55,738  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-04 10:03:55,747  INFO: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告
2025-06-04 10:03:55,747  INFO: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250604_100355
2025-06-04 10:03:55,749  INFO: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250604_100355/superlink_rx_gauss_report_20250604_100355.xlsx
2025-06-04 10:03:55,752  INFO: 开始测试
2025-06-04 10:03:55,753  ERROR: cur_tc: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 1, 'signal_ch': 0}
2025-06-04 10:04:01,997  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-04 10:04:56,897  INFO: result: per: 0.31633652971232584, polar_err: 9.3994e-03~4.7119e-02
2025-06-04 10:04:56,935  ERROR: cur_tc: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 2, 'signal_ch': 0}
2025-06-04 10:05:03,187  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym1db_grdc.wv"
2025-06-04 10:06:46,807  INFO: result: per: 0.12403450051493303, polar_err: 1.9531e-03~1.2329e-02
2025-06-04 10:06:46,836  ERROR: cur_tc: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 3, 'signal_ch': 0}
2025-06-04 10:06:53,191  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym2db_grdc.wv"
2025-06-04 10:08:36,587  INFO: result: per: 0.032813099654599, polar_err: 4.8828e-04~3.6621e-03
2025-06-04 10:08:36,615  ERROR: cur_tc: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 4, 'signal_ch': 0}
2025-06-04 10:08:42,871  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym3db_grdc.wv"
2025-06-04 10:10:48,073  INFO: result: per: 0.004385686137418188, polar_err: 0.0000e+00~6.1035e-04
2025-06-04 10:10:48,109  ERROR: cur_tc: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 5, 'signal_ch': 0}
2025-06-04 10:10:54,458  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym4db_grdc.wv"
2025-06-04 10:12:38,080  INFO: result: per: 0.0002539360081259545, polar_err: 0.0000e+00~2.4414e-04
2025-06-04 10:12:38,116  ERROR: cur_tc: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 6, 'signal_ch': 0}
2025-06-04 10:12:44,363  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym5db_grdc.wv"
2025-06-04 10:14:27,783  INFO: result: per: 0.0, polar_err: 0.0000e+00~0.0000e+00
2025-06-04 10:14:27,821  ERROR: cur_tc: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 3, 'snr': 1, 'signal_ch': 0}
2025-06-04 10:14:33,850  ERROR: ARB 文件不存在
2025-06-04 10:14:33,851  ERROR: cur_tc: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 3, 'snr': 2, 'signal_ch': 0}
2025-06-04 10:14:35,079  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar3_8\qpsk_1Rs_24Fs_1000fps_PType1_seed0_5bk_sym1db_grdc.wv"
2025-06-04 10:16:19,546  INFO: result: per: 0.1955419807642319, polar_err: 1.9409e-02~2.8687e-02
2025-06-04 10:16:19,581  ERROR: cur_tc: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 3, 'snr': 3, 'signal_ch': 0}
2025-06-04 10:16:25,890  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar3_8\qpsk_1Rs_24Fs_1000fps_PType1_seed0_5bk_sym2db_grdc.wv"
2025-06-04 10:18:09,464  INFO: result: per: 0.0487867014839819, polar_err: 2.6855e-03~7.2021e-03
2025-06-04 10:18:09,504  ERROR: cur_tc: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 3, 'snr': 4, 'signal_ch': 0}
2025-06-04 10:18:15,810  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar3_8\qpsk_1Rs_24Fs_1000fps_PType1_seed0_5bk_sym3db_grdc.wv"
2025-06-04 10:19:59,472  INFO: result: per: 0.007440381558028619, polar_err: 4.8828e-04~1.5869e-03
2025-06-04 10:19:59,512  INFO: 测试结束
2025-06-04 10:19:59,515  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-06-04 10:19:59,515  INFO: 测试进度：1/1
2025-06-04 10:20:04,519  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-06-04 10:20:04,523  INFO: 测试完成！总共测试耗时：00:14:32
2025-06-04 10:45:26,586  INFO: 测试开始!
2025-06-04 10:45:26,588  INFO: 测试进度：0/1
2025-06-04 10:45:26,588  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-04 10:45:26,589  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-04 10:45:26,684  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-04 10:45:26,693  INFO: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告
2025-06-04 10:45:26,694  INFO: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250604_104526
2025-06-04 10:45:26,702  INFO: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250604_104526/superlink_rx_gauss_report_20250604_104526.xlsx
2025-06-04 10:45:26,703  INFO: 开始测试
2025-06-04 10:45:26,703  ERROR: cur_tc: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 1, 'signal_ch': 0}
2025-06-04 10:45:32,947  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-04 10:47:37,762  INFO: result: per: 0.314938959847576, polar_err: 7.8125e-03~3.1616e-02
2025-06-04 10:47:37,801  ERROR: cur_tc: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 2, 'signal_ch': 0}
2025-06-04 10:47:44,055  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym1db_grdc.wv"
2025-06-04 10:49:27,454  INFO: result: per: 0.12631851420436158, polar_err: 1.4648e-03~7.6904e-03
2025-06-04 10:49:27,486  ERROR: cur_tc: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 3, 'signal_ch': 0}
2025-06-04 10:49:33,731  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym2db_grdc.wv"
2025-06-04 10:51:17,959  INFO: result: per: 0.031434796140678456, polar_err: 1.2207e-03~3.1738e-03
2025-06-04 10:51:18,001  ERROR: cur_tc: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 4, 'signal_ch': 0}
2025-06-04 10:51:24,247  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym3db_grdc.wv"
2025-06-04 10:53:07,999  INFO: result: per: 0.004248573240329789, polar_err: 0.0000e+00~8.5449e-04
2025-06-04 10:53:08,052  ERROR: cur_tc: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 5, 'signal_ch': 0}
2025-06-04 10:53:14,293  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym4db_grdc.wv"
2025-06-04 10:54:58,168  INFO: result: per: 6.298419096806285e-05, polar_err: 0.0000e+00~2.4414e-04
2025-06-04 10:54:58,208  ERROR: cur_tc: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 6, 'signal_ch': 0}
2025-06-04 10:55:04,451  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym5db_grdc.wv"
2025-06-04 10:56:43,157  INFO: result: per: 0.0, polar_err: 0.0000e+00~0.0000e+00
2025-06-04 10:56:43,191  ERROR: cur_tc: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 3, 'snr': 1, 'signal_ch': 0}
2025-06-04 10:56:49,278  ERROR: ARB 文件不存在
2025-06-04 10:56:49,316  ERROR: cur_tc: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 3, 'snr': 2, 'signal_ch': 0}
2025-06-04 10:56:50,607  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar3_8\qpsk_1Rs_24Fs_1000fps_PType1_seed0_5bk_sym1db_grdc.wv"
2025-06-04 10:58:34,003  INFO: result: per: 0.19563375692407947, polar_err: 1.9531e-02~3.1982e-02
2025-06-04 10:58:34,048  ERROR: cur_tc: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 3, 'snr': 3, 'signal_ch': 0}
2025-06-04 10:58:40,316  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar3_8\qpsk_1Rs_24Fs_1000fps_PType1_seed0_5bk_sym2db_grdc.wv"
2025-06-04 11:00:23,808  INFO: result: per: 0.047251133388672484, polar_err: 2.4414e-03~6.4697e-03
2025-06-04 11:00:23,849  ERROR: cur_tc: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 3, 'snr': 4, 'signal_ch': 0}
2025-06-04 11:00:30,112  INFO: file: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar3_8\qpsk_1Rs_24Fs_1000fps_PType1_seed0_5bk_sym3db_grdc.wv"
2025-06-04 11:02:13,518  INFO: result: per: 0.00843450479233232, polar_err: 8.5449e-04~2.0752e-03
2025-06-04 11:02:13,560  INFO: 测试结束
2025-06-04 11:02:13,563  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-06-04 11:02:13,563  INFO: 测试进度：1/1
2025-06-04 11:02:18,563  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-06-04 11:02:18,566  INFO: 测试完成！总共测试耗时：00:16:49
2025-06-04 14:53:18,174  INFO: 测试开始!
2025-06-04 14:53:18,180  INFO: 测试进度：0/1
2025-06-04 14:53:18,181  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-04 14:53:18,181  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-04 14:53:18,224  ERROR: 测试终止！测试错误：测试用例执行异常( attempted relative import with no known parent package )
