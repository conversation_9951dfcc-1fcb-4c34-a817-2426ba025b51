2025-06-03 10:21:18,003  INFO: 测试开始!
2025-06-03 10:21:18,004  INFO: 测试进度：0/1
2025-06-03 10:21:18,005  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-03 10:21:18,005  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-03 10:21:18,108  INFO: bluetooth_tester host_id: <PERSON><PERSON><PERSON><PERSON>,CMW,1201.0002k75/101147,3.7.171
2025-06-03 10:21:18,119  INFO: 开始测试
2025-06-03 10:21:18,131  INFO: Generator State: ON
2025-06-03 10:21:23,798  INFO: 测试结束
2025-06-03 10:21:23,802  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-06-03 10:21:23,802  INFO: 测试进度：1/1
2025-06-03 10:21:28,807  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-06-03 10:21:28,812  INFO: 测试完成！总共测试耗时：00:00:11
2025-06-03 10:22:36,800  INFO: 测试开始!
2025-06-03 10:22:36,803  INFO: 测试进度：0/1
2025-06-03 10:22:36,803  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-03 10:22:36,804  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-03 10:22:36,902  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-03 10:22:36,916  INFO: 开始测试
2025-06-03 10:22:36,929  INFO: Generator State: ON
2025-06-03 10:22:42,670  INFO: 测试结束
2025-06-03 10:22:42,673  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-06-03 10:22:42,673  INFO: 测试进度：1/1
2025-06-03 10:22:47,677  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-06-03 10:22:47,682  INFO: 测试完成！总共测试耗时：00:00:11
2025-06-03 10:28:01,891  INFO: 测试开始!
2025-06-03 10:28:01,894  INFO: 测试进度：0/1
2025-06-03 10:28:01,894  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-03 10:28:01,895  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-03 10:28:01,983  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-03 10:28:01,993  INFO: 开始测试
2025-06-03 10:28:02,004  INFO: Generator State: ON
2025-06-03 10:28:35,612  INFO: 测试结束
2025-06-03 10:28:35,616  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-06-03 10:28:35,616  INFO: 测试进度：1/1
2025-06-03 10:28:40,621  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-06-03 10:28:40,624  INFO: 测试完成！总共测试耗时：00:00:39
2025-06-03 13:21:27,026  INFO: 测试开始!
2025-06-03 13:21:27,028  INFO: 测试进度：0/1
2025-06-03 13:21:27,029  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-03 13:21:27,029  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-03 13:21:27,120  ERROR: 测试终止！测试错误：测试用例执行异常( cannot import name 'first' from 'zmq' (/Users/<USER>/.virtualenvs/gxbtt_env/lib/python3.8/site-packages/zmq/__init__.py) )
2025-06-03 13:22:33,951  INFO: 测试开始!
2025-06-03 13:22:33,953  INFO: 测试进度：0/1
2025-06-03 13:22:33,954  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-03 13:22:33,955  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-03 13:22:34,053  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-03 13:22:34,062  INFO: 开始测试
2025-06-03 13:22:34,073  INFO: Generator State: ON
2025-06-03 13:22:36,172  INFO: level: INV
2025-06-03 13:22:36,372  ERROR: 测试终止！测试错误：测试用例执行异常( 'CmwFunc' object has no attribute 'cmw_func_gprf_generator_create_arb_file_path_from_params' )
2025-06-03 13:26:33,275  INFO: 测试开始!
2025-06-03 13:26:33,278  INFO: 测试进度：0/1
2025-06-03 13:26:33,278  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-03 13:26:33,279  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-03 13:26:33,346  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-03 13:26:33,357  INFO: 开始测试
2025-06-03 13:26:33,368  INFO: Generator State: OFF
2025-06-03 13:26:35,429  INFO: level: INV
2025-06-03 13:26:35,641  ERROR: 测试终止！测试错误：测试用例执行异常( name 'sagitta_symbol_rate' is not defined )
