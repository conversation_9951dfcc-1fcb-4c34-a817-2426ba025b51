from gxpy.gxengine.eventengine import Event, EventEngine, BaseData

EVENT_BTT_RUN_BTT = "eBttRunBtt"
EVENT_BTT_KILL_PROCESS = "eBttKillProcess"
EVENT_BTT_RUNTIME_LOG = "eBttRunTimeLog"
EVENT_BTT_FINISH_LOG = "eBttFinishLog"


class BttRunData(BaseData):
    """
    """
    def __init__(self, data: dict):
        self.data = data

    def __getitem__(self, key):
        """支持字典式访问"""
        return self.data[key]

    def __setitem__(self, key, value):
        """支持字典式设置"""
        self.data[key] = value

    def __contains__(self, key):
        """支持in操作符"""
        return key in self.data

    def get(self, key, default=None):
        """支持get方法"""
        return self.data.get(key, default)


class BttKillProcessData(BaseData):
    """
    """
    def __init__(self):
        pass


class BttRunTimeLogData(BaseData):
    """
    """
    def __init__(self, test_case_total: int, test_case_index: int):
        self.test_case_total = test_case_total
        self.test_case_index = test_case_index


class BttFinishLogData(BaseData):
    """
    """

    def __init__(self, msg: str, result: bool):
        self.msg = msg
        self.result = result
